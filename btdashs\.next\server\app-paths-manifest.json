{"/api/skills/route": "app/api/skills/route.js", "/api/user/experiences/route": "app/api/user/experiences/route.js", "/api/user/me/route": "app/api/user/me/route.js", "/api/user/educations/route": "app/api/user/educations/route.js", "/api/user/preferences/route": "app/api/user/preferences/route.js", "/api/user/skills/route": "app/api/user/skills/route.js", "/api/user/events/route": "app/api/user/events/route.js", "/api/user/company/route": "app/api/user/company/route.js", "/api/user/jobs/route": "app/api/user/jobs/route.js", "/api/subnet-verification/user/challenge/route": "app/api/subnet-verification/user/challenge/route.js", "/api/subnet-verification/user/verify/route": "app/api/subnet-verification/user/verify/route.js", "/api/subnet-verification/user/subnets/route": "app/api/subnet-verification/user/subnets/route.js", "/page": "app/page.js", "/subnets/[id]/page": "app/subnets/[id]/page.js", "/profile/page": "app/profile/page.js"}