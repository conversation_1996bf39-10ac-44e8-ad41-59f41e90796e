{"/api/network-stats/latest/route": "app/api/network-stats/latest/route.js", "/api/subnet-verification/user/subnets/route": "app/api/subnet-verification/user/subnets/route.js", "/api/skills/route": "app/api/skills/route.js", "/api/categories/route": "app/api/categories/route.js", "/api/subnets/route": "app/api/subnets/route.js", "/api/companies/route": "app/api/companies/route.js", "/api/products/route": "app/api/products/route.js", "/api/events/route": "app/api/events/route.js", "/api/user/skills/route": "app/api/user/skills/route.js", "/api/user/me/route": "app/api/user/me/route.js", "/api/user/preferences/route": "app/api/user/preferences/route.js", "/api/user/educations/route": "app/api/user/educations/route.js", "/api/user/experiences/route": "app/api/user/experiences/route.js", "/api/user/events/route": "app/api/user/events/route.js", "/api/user/jobs/route": "app/api/user/jobs/route.js", "/api/user/company/route": "app/api/user/company/route.js", "/api/subnet-verification/user/challenge/route": "app/api/subnet-verification/user/challenge/route.js", "/api/subnet-verification/user/verify/route": "app/api/subnet-verification/user/verify/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/page": "app/page.js", "/profile/page": "app/profile/page.js", "/subnets/[id]/page": "app/subnets/[id]/page.js"}