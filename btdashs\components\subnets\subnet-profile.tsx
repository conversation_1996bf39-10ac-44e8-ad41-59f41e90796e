"use client";

import { Book, Github, Globe, Shield } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { Navigation, Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
// Use custom API route instead of @auth0/nextjs-auth0/client to avoid module export issues

import { AdBanner } from "@/components/ads-placements/ad-banner";
import { CategoryTag } from "@/components/category-tag";
import { SubnetDocumentation } from "@/components/subnet-documentation";
import { SubnetGithubContributionGraph } from "@/components/subnet-github-contribution-graph";
import { SubnetNews } from "@/components/subnet-news";
import { SubnetTeam } from "@/components/subnet-team";
import { SubnetValidators } from "@/components/subnet-validators";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, <PERSON>T<PERSON><PERSON> } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import tao from "@/public/tao-logo.svg";

import type { Category, Company, Event, Job, News, Product, Subnet, SubnetMetric } from "@/lib/db/models";
// Import the new chart component
import { SubnetRelationshipChart } from "@/components/subnets/subnet-relationship-chart";
import ReactMarkdown from "react-markdown";
import { SubnetApplications } from "../subnet-applications";

interface KeyFeature {
	title: string;
	description: string;
}

// Utility function to convert video URLs to embeddable format
const getEmbeddableVideoUrl = (url: string): string => {
	if (!url) return url;

	// YouTube URL conversion
	if (url.includes("youtube.com/watch?v=")) {
		const videoId = url.split("v=")[1]?.split("&")[0];
		return `https://www.youtube.com/embed/${videoId}`;
	}

	if (url.includes("youtu.be/")) {
		const videoId = url.split("youtu.be/")[1]?.split("?")[0];
		return `https://www.youtube.com/embed/${videoId}`;
	}

	// Vimeo URL conversion
	if (url.includes("vimeo.com/")) {
		const videoId = url.split("vimeo.com/")[1]?.split("?")[0];
		return `https://player.vimeo.com/video/${videoId}`;
	}

	// Return original URL if no conversion needed
	return url;
};

// Utility function to detect video type from URL
const getVideoType = (url: string): string => {
	if (!url) return "";

	if (url.includes("youtube.com") || url.includes("youtu.be")) {
		return "youtube";
	}

	if (url.includes("vimeo.com")) {
		return "vimeo";
	}

	return "other";
};

interface SubnetProfileProps {
	subnet: Subnet;
	metrics: SubnetMetric;
	subnetProfile: any; // Subnet profile data from server
	categories: Category[];
	news: News[];
	products: Product[];
	jobs: Job[];
	events: Event[];
	companies: Company[];
}

export default function SubnetProfile({
	subnet,
	metrics,
	subnetProfile,
	categories,
	news,
	products,
	jobs,
	events,
	companies,
}: SubnetProfileProps) {
	const router = useRouter();
	const [user, setUser] = useState<any>(null);
	const [userLoading, setUserLoading] = useState(true);
	const [isVerified, setIsVerified] = useState(false);
	const [checkingVerification, setCheckingVerification] = useState(false);

	// Fetch user data using custom API route
	useEffect(() => {
		const fetchUser = async () => {
			try {
				const response = await fetch("/api/user/me");
				if (response.ok) {
					const userData = await response.json();
					setUser(userData);
				}
			} catch (error) {
				console.error("Error fetching user:", error);
			} finally {
				setUserLoading(false);
			}
		};

		fetchUser();
	}, []);

	const netuid = subnet.netuid;

	// Check if current user has already verified this subnet
	useEffect(() => {
		const checkVerificationStatus = async () => {
			if (!user) return;

			setCheckingVerification(true);
			try {
				const response = await fetch(`/api/subnet-verification/user/subnets`);
				if (response.ok) {
					const data = await response.json();
					const verifiedSubnets = data.data || [];
					setIsVerified(verifiedSubnets.some((vs: any) => vs.netuid === netuid));
				}
			} catch (error) {
				console.error("Error checking verification status:", error);
			} finally {
				setCheckingVerification(false);
			}
		};

		checkVerificationStatus();
	}, [user, netuid]);

	const handleClaimOwnership = () => {
		// Navigate to profile page with subnet pre-selected
		router.push(`/profile?tab=subnet-verification&netuid=${netuid}`);
	};
	/* const images = subnet.images?.length
    ? subnet.images
    : [
        "https://via.placeholder.com/800x400?text=Image+1",
        "https://via.placeholder.com/800x400?text=Image+2",
        "https://via.placeholder.com/800x400?text=Image+3",
      ]; */

	const data = {
		companyCount: (companies?.length ?? 0) as number,
		productCount: (products?.length ?? 0) as number,
		eventCount: (events?.length ?? 0) as number,
		jobCount: (jobs?.length ?? 0) as number,
		categoryCount: (categories?.length ?? 0) as number,
		validatorCount: (metrics?.validators_count ?? 0) as number,
		newsCount: (news.length ?? 0) as number,
		subnetCount: (subnet?.subnet_ids?.length ?? 0) as number,
	};
	const metricsCards = (
		<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
			<Card>
				<CardHeader>
					<CardTitle>Price</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="text-3xl font-bold">
						<Image src={tao} alt="TAO" width={24} height={24} className="inline-block pr-2" />
						{metrics?.alpha_price_tao != null && !isNaN(metrics.alpha_price_tao)
							? Number(metrics.alpha_price_tao) < 0.01
								? Number(metrics.alpha_price_tao).toFixed(3)
								: Number(metrics.alpha_price_tao).toFixed(2)
							: "0.00"}
					</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>Validators</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="text-3xl font-bold">{metrics?.validators_count ?? 0}</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>Emission</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="text-3xl font-bold">{((metrics?.emission ?? 0) / 1e7).toFixed(2)}%</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>Miners</CardTitle>
				</CardHeader>
				<CardContent>
					<div
						className={`text-3xl font-bold ${
							subnet.active_miners <= 5
								? "text-red-500"
								: subnet.active_miners <= 15
								? "text-orange-500"
								: "text-green-500"
						}`}
					>
						{subnet.active_miners || 0}
					</div>
				</CardContent>
			</Card>
		</div>
	);

	return (
		<>
			<div className="py-8 px-6 sm:px-8 lg:px-12">
				{/* Top Horizontal Ad */}
				<div className="w-full mb-8" style={{ minHeight: "90px" }}>
					<AdBanner variant="horizontal" className="mx-auto" adSlot="7230250579" />
				</div>

				{/* Banner Image */}
				{subnet.banner_url && (
					<div className="mb-8 w-full h-32 md:h-40 lg:h-52 rounded-lg overflow-hidden shadow-lg bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
						<img
							src={subnet.banner_url}
							alt={`${subnet.name} banner`}
							className="max-w-full max-h-full object-contain"
						/>
					</div>
				)}

				{/* Hero Section */}
				<div className="mb-8 grid grid-cols-1 lg:grid-cols-2 gap-8">
					<div>
						<div className="flex items-center gap-4 mb-4">
							<div className="w-16 h-16 rounded-full bg-primary flex items-center justify-center text-black text-3xl font-bold">
								{subnet.subnet_symbol || subnet.name.charAt(0)}
							</div>

							<div>
								<h1 className="text-3xl font-bold">{subnet.name}</h1>
								<p className="text-muted-foreground">Subnet ID: {netuid}</p>
								<p className="text-xs text-muted-foreground">
									Coldkey:
									<a
										href={`https://taostats.io/account/${subnet.sub_address_pkey}/?utm_source=dynamictoamarketcap&utm_medium=referral&utm_campaign=subnet_profile`}
										target="_blank"
										rel="noopener noreferrer"
										className="text-xs text-muted-foreground underline hover:text-primary"
									>
										{subnet.sub_address_pkey}
									</a>
								</p>
								<div className="mt-3 flex flex-wrap gap-2 text-muted-foreground">
									{categories && categories.length > 0
										? categories.map((category, id) => (
												<CategoryTag key={id} category={category.name} />
										  ))
										: null}
								</div>
							</div>
						</div>

						<div className="my-4">
							<ReactMarkdown>{subnet.description}</ReactMarkdown>
						</div>

						<div className="flex gap-4 mb-8">
							{subnet.white_paper ? (
								<Button asChild size="sm" className="gap-2" variant="default">
									<a href={subnet.white_paper} target="_blank" rel="noopener noreferrer">
										<Book className="h-5 w-5" />
										Read White Paper
									</a>
								</Button>
							) : (
								<Button size="sm" className="gap-2" disabled>
									<Book className="h-5 w-5" />
									White Paper Unavailable
								</Button>
							)}

							{/* Claim Subnet Ownership Button */}
							{user && !userLoading && !isVerified && !checkingVerification && (
								<Button onClick={handleClaimOwnership} size="sm" variant="outline" className="gap-2">
									<Shield className="h-4 w-4" />
									Claim Subnet Ownership
								</Button>
							)}
						</div>
						{subnet.images || subnet.main_video_url ? (
							<div className="flex flex-wrap gap-2 overflow-hidden">{metricsCards}</div>
						) : null}
					</div>

					{/* Media: video or image carousel */}
					{subnet.main_video_url ? (
						<div>
							<div className="relative pb-[56.25%] h-0 overflow-hidden rounded-lg shadow-lg border border-slate-200 dark:border-slate-700">
								<iframe
									className="absolute left-0 w-full h-full"
									src={getEmbeddableVideoUrl(subnet.main_video_url)}
									title="Subnet video"
									frameBorder="0"
									allow="autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
									allowFullScreen
								/>
							</div>
							<div className="grid grid-cols-1 sm:grid-cols-2 gap-4 pt-4">
								{(subnet.website_perm || subnet.website) && (
									<a
										href={subnet.website_perm || subnet.website}
										target="_blank"
										rel="noopener noreferrer"
										className="group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200"
									>
										<div className="flex-shrink-0 w-12 h-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center">
											<Globe className="h-6 w-6 text-blue-600 dark:text-blue-400" />
										</div>
										<div className="flex-grow">
											<h3 className="font-medium text-slate-900 dark:text-slate-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
												Official Website
											</h3>
											<p className="text-sm text-slate-500 dark:text-slate-400 truncate">
												{(() => {
													const websiteUrl = subnet.website_perm || subnet.website;
													return websiteUrl
														? `${websiteUrl
																.replace(/^https?:\/\/(www\.)?/, "")
																.slice(0, 30)}${
																websiteUrl.replace(/^https?:\/\/(www\.)?/, "").length >
																30
																	? "..."
																	: ""
														  }`
														: "";
												})()}
											</p>
										</div>
									</a>
								)}

								<a
									href={subnet.github_repo || "https://github.com/example/subnet-repo"}
									target="_blank"
									rel="noopener noreferrer"
									className="group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200"
								>
									<div className="flex-shrink-0 w-12 h-12 rounded-full bg-purple-50 dark:bg-purple-900/30 flex items-center justify-center">
										<Github className="h-6 w-6 text-purple-600 dark:text-purple-400" />
									</div>
									<div className="flex-grow">
										<h3 className="font-medium text-slate-900 dark:text-slate-100 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
											GitHub Repository
										</h3>
										<p className="text-sm text-slate-500 dark:text-slate-400 truncate">
											{subnet.github_repo
												? `${subnet.github_repo
														.replace("https://github.com/", "")
														.slice(0, 30)}${
														subnet.github_repo.replace("https://github.com/", "").length >
														40
															? "..."
															: ""
												  }`
												: "github.com/example/subnet-repo"}
										</p>
									</div>
								</a>
							</div>
						</div>
					) : !subnet.main_video_url ? (
						<div>
							{subnet.images ? null : <div className="gap-2 overflow-hidden">{metricsCards}</div>}
							<div className="rounded-lg overflow-hidden shadow-lg">
								<Swiper
									modules={[Navigation, Pagination]}
									navigation
									pagination={{ clickable: true }}
									spaceBetween={10}
									slidesPerView={1}
									className="w-full h-full"
								>
									{subnet.images?.map((image: string, index: number) => (
										<SwiperSlide key={index}>
											<Image
												src={image}
												alt={`Subnet Image ${index + 1}`}
												width={800}
												height={400}
												className="w-full h-auto object-cover"
											/>
										</SwiperSlide>
									))}
								</Swiper>
							</div>
							<div className="grid grid-cols-1 sm:grid-cols-2 gap-4 pt-4">
								{(subnet.website_perm || subnet.website) && (
									<a
										href={subnet.website_perm || subnet.website}
										target="_blank"
										rel="noopener noreferrer"
										className="group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200"
									>
										<div className="flex-shrink-0 w-12 h-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center">
											<Globe className="h-6 w-6 text-blue-600 dark:text-blue-400" />
										</div>
										<div className="flex-grow">
											<h3 className="font-medium text-slate-900 dark:text-slate-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
												Official Website
											</h3>
											<p className="text-sm text-slate-500 dark:text-slate-400 truncate">
												{(() => {
													const websiteUrl = subnet.website_perm || subnet.website;
													return websiteUrl
														? `${websiteUrl
																.replace(/^https?:\/\/(www\.)?/, "")
																.slice(0, 30)}${
																websiteUrl.replace(/^https?:\/\/(www\.)?/, "").length >
																30
																	? "..."
																	: ""
														  }`
														: "";
												})()}
											</p>
										</div>
									</a>
								)}

								<a
									href={subnet.github_repo || "https://github.com/example/subnet-repo"}
									target="_blank"
									rel="noopener noreferrer"
									className="group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200"
								>
									<div className="flex-shrink-0 w-12 h-12 rounded-full bg-purple-50 dark:bg-purple-900/30 flex items-center justify-center">
										<Github className="h-6 w-6 text-purple-600 dark:text-purple-400" />
									</div>
									<div className="flex-grow">
										<h3 className="font-medium text-slate-900 dark:text-slate-100 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
											GitHub Repository
										</h3>
										<p className="text-sm text-slate-500 dark:text-slate-400 truncate">
											{subnet.github_repo
												? `${subnet.github_repo
														.replace("https://github.com/", "")
														.slice(0, 30)}${
														subnet.github_repo.replace("https://github.com/", "").length >
														40
															? "..."
															: ""
												  }`
												: "github.com/example/subnet-repo"}
										</p>
									</div>
								</a>
							</div>
						</div>
					) : (
						<div>
							<div className=" gap-4 overflow-hidden max-[60px]">{metricsCards}</div>
							<div className="grid grid-cols-1 sm:grid-cols-2 gap-4 pt-4">
								{(subnet.website || subnet.website_perm) && (
									<a
										href={subnet.website || subnet.website_perm}
										target="_blank"
										rel="noopener noreferrer"
										className="group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200"
									>
										<div className="flex-shrink-0 w-12 h-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center">
											<Globe className="h-6 w-6 text-blue-600 dark:text-blue-400" />
										</div>
										<div className="flex-grow">
											<h3 className="font-medium text-slate-900 dark:text-slate-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
												Official Website
											</h3>
											<p className="text-sm text-slate-500 dark:text-slate-400 truncate">
												{(() => {
													const websiteUrl = subnet.website || subnet.website_perm;
													return websiteUrl
														? `${websiteUrl
																.replace(/^https?:\/\/(www\.)?/, "")
																.slice(0, 30)}${
																websiteUrl.replace(/^https?:\/\/(www\.)?/, "").length >
																30
																	? "..."
																	: ""
														  }`
														: "";
												})()}
											</p>
										</div>
									</a>
								)}

								<a
									href={subnet.github_repo || "https://github.com/example/subnet-repo"}
									target="_blank"
									rel="noopener noreferrer"
									className="group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200"
								>
									<div className="flex-shrink-0 w-12 h-12 rounded-full bg-purple-50 dark:bg-purple-900/30 flex items-center justify-center">
										<Github className="h-6 w-6 text-purple-600 dark:text-purple-400" />
									</div>
									<div className="flex-grow">
										<h3 className="font-medium text-slate-900 dark:text-slate-100 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
											GitHub Repository
										</h3>
										<p className="text-sm text-slate-500 dark:text-slate-400 truncate">
											{subnet.github_repo
												? `${subnet.github_repo
														.replace("https://github.com/", "")
														.slice(0, 30)}${
														subnet.github_repo.replace("https://github.com/", "").length >
														40
															? "..."
															: ""
												  }`
												: "github.com/example/subnet-repo"}
										</p>
									</div>
								</a>
							</div>
						</div>
					)}
				</div>

				{/* Ecosystem Overview Section */}
				<div className="grid grid-cols-7 gap-4 mb-4">
					{/* GitHub Contributions – 3/4 width */}
					<div className="col-span-5">
						<SubnetGithubContributionGraph
							className="h-[360px]"
							contributions={metrics?.github_contributions?.data || []}
						/>
					</div>

					{/* Relationship Chart – 1/4 width */}
					<div className="h-[360px] col-span-2 overflow-visible">
						<SubnetRelationshipChart subnetId={subnet.name} data={data} className="h-full" />
					</div>
				</div>

				{/* Applications Section */}
				<div className="mt-0 mb-8">
					<SubnetApplications products={products} />
				</div>

				{/* Tabs */}
				<Tabs defaultValue="overview" className="space-y-4">
					<TabsList className="flex flex-wrap">
						<TabsTrigger value="overview">Overview</TabsTrigger>
						<TabsTrigger value="team">Team</TabsTrigger>
						<TabsTrigger value="documentation">Documentation</TabsTrigger>
						<TabsTrigger value="validators">Validators</TabsTrigger>
						<TabsTrigger value="news">News</TabsTrigger>
					</TabsList>

					<div className="grid grid-cols-1 lg:grid-cols-1 gap-8">
						<div className="lg:col-span-3">
							<TabsContent value="overview" className="space-y-8">
								<Card>
									<CardHeader>
										<CardTitle className="text-lg">Key Features</CardTitle>
									</CardHeader>
									<CardContent className="space-y-4">
										<div className="grid md:grid-cols-2 gap-4">
											{subnet.key_features && subnet.key_features.length > 0 ? (
												(() => {
													// Handle different key_features formats
													let featuresArray: KeyFeature[] = [];

													if (Array.isArray(subnet.key_features)) {
														// Check if it's a nested array format [[{title, description}]]
														if (Array.isArray(subnet.key_features[0])) {
															featuresArray = subnet.key_features[0];
														} else {
															// Direct array format [{title, description}]
															featuresArray = subnet.key_features;
														}
													}

													return featuresArray.map(
														(feature: KeyFeature, id: number) =>
															feature && (
																<div className="space-y-2 pb-4" key={id}>
																	<h4 className="font-medium">{feature.title}</h4>
																	<p className="text-muted-foreground">
																		{feature.description}
																	</p>
																</div>
															)
													);
												})()
											) : (
												<p className="text-muted-foreground">No key features available.</p>
											)}
										</div>
									</CardContent>
								</Card>
							</TabsContent>

							<TabsContent value="team">
								<SubnetTeam subnet={subnet} />
							</TabsContent>
							<TabsContent value="documentation">
								<SubnetDocumentation />
							</TabsContent>
							<TabsContent value="validators">
								<SubnetValidators />
							</TabsContent>
							<TabsContent value="news">
								<SubnetNews news={news} />
							</TabsContent>
						</div>
					</div>
				</Tabs>

				{/* Bottom Horizontal Ad */}
				<div className="flex-1 mt-8 mb-8 min-h-[90px] w-full">
					<AdBanner variant="horizontal" className="w-full h-full" adSlot="2282987717" />
				</div>
			</div>
		</>
	);
}
