# Subnet Ownership Verification System

## Overview

The Subnet Ownership Verification System allows company owners to cryptographically prove ownership of Bittensor subnets and link them to their company profiles. This system uses wallet-based signature verification to ensure only legitimate subnet owners can claim ownership.

## Architecture

### Components

1. **Backend Services**

    - `SubnetVerificationService`: Core verification logic
    - `SecurityMonitoringService`: Security monitoring and threat detection
    - API Controllers and Routes: RESTful endpoints

2. **Frontend Components**

    - `WalletConnector`: Polkadot wallet integration
    - `SubnetVerification`: Verification workflow UI
    - `VerifiedSubnets`: Management interface

3. **Database Schema**
    - `subnet_verification_challenges`: Temporary challenges
    - `verified_subnet_ownership`: Verified ownership records
    - `subnet_verification_attempts`: Security monitoring logs

## Verification Workflow

### Step 1: Challenge Generation

1. User selects a subnet to verify
2. System generates a unique challenge message containing:
    - Subnet netuid
    - Company ID
    - Unique nonce
    - Timestamp
    - Expiry time (15 minutes)

### Step 2: Wallet Signing

1. User connects their Polkadot wallet
2. User signs the challenge message with their wallet
3. Signature is captured for verification

### Step 3: Verification

1. System verifies the cryptographic signature
2. System checks that the signing address matches the subnet owner
3. System stores the verified ownership record
4. Company's netuid array is updated

## API Endpoints

### Generate Challenge

```
POST /api/subnet-verification/challenge
```

**Request Body:**

```json
{
	"netuid": 1,
	"companyId": 123
}
```

**Response:**

```json
{
	"success": true,
	"data": {
		"challengeId": 456,
		"message": "BTDash Subnet Verification\nSubnet: 1\nCompany: 123\n...",
		"nonce": "abc123...",
		"expiresAt": "2025-01-30T15:30:00Z",
		"netuid": 1,
		"companyId": 123
	}
}
```

### Verify Ownership

```
POST /api/subnet-verification/verify
```

**Request Body:**

```json
{
	"challengeId": 456,
	"signature": "0x1234567890abcdef...",
	"signerAddress": "5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY",
	"companyId": 123
}
```

**Response:**

```json
{
	"success": true,
	"data": {
		"verificationId": 789,
		"netuid": 1,
		"ownerAddress": "5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY",
		"verifiedAt": "2025-01-30T15:25:00Z"
	}
}
```

### Get Verified Subnets

```
GET /api/subnet-verification/company/{companyId}
```

**Response:**

```json
{
	"success": true,
	"data": [
		{
			"verification_id": 789,
			"netuid": 1,
			"owner_address": "5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY",
			"verified_at": "2025-01-30T15:25:00Z",
			"subnet_name": "Example Subnet",
			"description": "An example subnet"
		}
	]
}
```

### Remove Verification

```
DELETE /api/subnet-verification/company/{companyId}/subnet/{netuid}
```

## Security Features

### Rate Limiting

-   Maximum 5 challenge requests per 5 minutes per IP
-   Maximum 10 verification attempts per 15 minutes per IP
-   Maximum 5 verification attempts per hour per user

### Challenge Security

-   Challenges expire after 15 minutes
-   Each challenge contains a unique nonce
-   Challenges are single-use only
-   Automatic cleanup of expired challenges

### Signature Verification

-   Uses Polkadot.js cryptographic libraries
-   Verifies signature authenticity
-   Checks signing address against on-chain subnet owner
-   Prevents replay attacks

### Monitoring and Logging

-   All verification attempts are logged
-   Security events are monitored
-   Suspicious activity detection
-   Automatic alerting for anomalies

## Database Schema

### subnet_verification_challenges

```sql
CREATE TABLE dtm_base.subnet_verification_challenges (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES dtm_base.users(id),
    netuid INTEGER NOT NULL,
    challenge_message TEXT NOT NULL,
    nonce VARCHAR(64) NOT NULL UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    is_used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### verified_subnet_ownership

```sql
CREATE TABLE dtm_base.verified_subnet_ownership (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES dtm_base.users(id),
    company_id INTEGER NOT NULL REFERENCES dtm_base.companies(id),
    netuid INTEGER NOT NULL,
    owner_address VARCHAR(255) NOT NULL,
    verification_signature TEXT NOT NULL,
    verification_message TEXT NOT NULL,
    verified_at TIMESTAMP DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
    -- Note: Multiple users can verify ownership of the same subnet
);
```

### subnet_verification_attempts

```sql
CREATE TABLE dtm_base.subnet_verification_attempts (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES dtm_base.users(id),
    netuid INTEGER NOT NULL,
    attempted_address VARCHAR(255),
    challenge_id INTEGER REFERENCES dtm_base.subnet_verification_challenges(id),
    success BOOLEAN NOT NULL,
    failure_reason TEXT,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);
```

## Error Handling

### Common Error Responses

**Authentication Required (401)**

```json
{
	"success": false,
	"message": "Authentication required"
}
```

**Insufficient Permissions (403)**

```json
{
	"success": false,
	"message": "Insufficient permissions to verify subnets for this company"
}
```

**Invalid Challenge (400)**

```json
{
	"success": false,
	"message": "Invalid or expired challenge"
}
```

**Invalid Signature (400)**

```json
{
	"success": false,
	"message": "Invalid signature. Please check your wallet signature."
}
```

**Not Subnet Owner (400)**

```json
{
	"success": false,
	"message": "The signing address is not the owner of this subnet."
}
```

**Rate Limit Exceeded (429)**

```json
{
	"success": false,
	"message": "Rate limit exceeded. Please try again later."
}
```

## Frontend Integration

### Wallet Connection

```typescript
import { useWallet } from "@/hooks/useWallet";

const { connectWallet, selectedAccount, signMessage } = useWallet();
```

### Verification Component

```typescript
import { SubnetVerification } from "@/components/subnet/SubnetVerification";

<SubnetVerification company={company} subnets={availableSubnets} onVerificationComplete={handleComplete} />;
```

### Managing Verified Subnets

```typescript
import { VerifiedSubnets } from "@/components/company/VerifiedSubnets";

<VerifiedSubnets company={company} userRole={userRole} />;
```

## Deployment Considerations

```
### Dependencies
Ensure the following packages are installed:
- Backend: `@polkadot/util-crypto`, `@polkadot/util`, `crypto-js`
- Frontend: `@polkadot/extension-dapp`, `@polkadot/util-crypto`, `@polkadot/api`

## Monitoring and Maintenance

### Scheduled Tasks
- Cleanup expired challenges: Every hour
- Security monitoring: Every 15 minutes
- Old records cleanup: Daily

### Metrics to Monitor
- Verification success rate
- Failed attempt frequency
- Challenge generation rate
- Active verifications count

### Security Alerts
- High failure rates (>50% failures with >10 attempts)
- Rapid challenge generation (>3 per minute)
- Multiple address attempts per user
- IP-based suspicious activity
```
