/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/subnet-verification/user/verify/route";
exports.ids = ["app/api/subnet-verification/user/verify/route"];
exports.modules = {

/***/ "(rsc)/./app/api/subnet-verification/user/verify/route.ts":
/*!**********************************************************!*\
  !*** ./app/api/subnet-verification/user/verify/route.ts ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth0__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../../lib/auth0 */ \"(rsc)/./lib/auth0.ts\");\n\n\nconst API_BASE_URL = process.env.API_BASE_URL;\nconst INTERNAL_API_KEY = process.env.INTERNAL_API_KEY;\nasync function POST(request) {\n    try {\n        // Get access token\n        const { token: accessToken } = await _lib_auth0__WEBPACK_IMPORTED_MODULE_1__.auth0.getAccessToken();\n        if (!accessToken) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/api/auth/login\", request.url));\n        }\n        // Get request body\n        const body = await request.json();\n        const { challengeId, signature, signerAddress } = body;\n        // Validate input\n        if (!challengeId || !signature || !signerAddress) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: \"challengeId, signature, and signerAddress are required\"\n            }, {\n                status: 400\n            });\n        }\n        // Forward request to backend API\n        const backendResponse = await fetch(`${API_BASE_URL}/subnet-verification/user/verify`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${accessToken}`,\n                \"x-internal-api-key\": INTERNAL_API_KEY || \"\"\n            },\n            body: JSON.stringify({\n                challengeId,\n                signature,\n                signerAddress\n            })\n        });\n        const data = await backendResponse.json();\n        if (!backendResponse.ok) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: data.message || \"Failed to verify ownership\"\n            }, {\n                status: backendResponse.status\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: data.data,\n            message: data.message || \"Ownership verified successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error verifying user ownership:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/subnet-verification/user/verify/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth0.ts":
/*!**********************!*\
  !*** ./lib/auth0.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth0: () => (/* binding */ auth0)\n/* harmony export */ });\n/* harmony import */ var _auth0_nextjs_auth0_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth0/nextjs-auth0/server */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/index.js\");\n// lib/auth0.js\n\n// Initialize the Auth0 client\nconst auth0 = new _auth0_nextjs_auth0_server__WEBPACK_IMPORTED_MODULE_0__.Auth0Client({\n    session: {\n        rolling: true,\n        cookie: {\n            name: \"app_session\",\n            path: \"/\",\n            sameSite: \"lax\",\n            secure: \"development\" === \"production\"\n        }\n    },\n    authorizationParameters: {\n        scope: process.env.AUTH0_SCOPE,\n        audience: process.env.AUTH0_AUDIENCE\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvYXV0aDAudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxlQUFlO0FBRTBDO0FBRXpELDhCQUE4QjtBQUN2QixNQUFNQyxRQUFRLElBQUlELG1FQUFXQSxDQUFDO0lBQ3BDRSxTQUFTO1FBQ1JDLFNBQVM7UUFDVEMsUUFBUTtZQUNQQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsVUFBVTtZQUNWQyxRQUFRQyxrQkFBeUI7UUFDbEM7SUFDRDtJQUNBQyx5QkFBeUI7UUFDeEJDLE9BQU9GLFFBQVFHLEdBQUcsQ0FBQ0MsV0FBVztRQUM5QkMsVUFBVUwsUUFBUUcsR0FBRyxDQUFDRyxjQUFjO0lBQ3JDO0FBQ0QsR0FBRyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxsaWJcXGF1dGgwLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGxpYi9hdXRoMC5qc1xyXG5cclxuaW1wb3J0IHsgQXV0aDBDbGllbnQgfSBmcm9tIFwiQGF1dGgwL25leHRqcy1hdXRoMC9zZXJ2ZXJcIjtcclxuXHJcbi8vIEluaXRpYWxpemUgdGhlIEF1dGgwIGNsaWVudFxyXG5leHBvcnQgY29uc3QgYXV0aDAgPSBuZXcgQXV0aDBDbGllbnQoe1xyXG5cdHNlc3Npb246IHtcclxuXHRcdHJvbGxpbmc6IHRydWUsXHJcblx0XHRjb29raWU6IHtcclxuXHRcdFx0bmFtZTogXCJhcHBfc2Vzc2lvblwiLFxyXG5cdFx0XHRwYXRoOiBcIi9cIixcclxuXHRcdFx0c2FtZVNpdGU6IFwibGF4XCIsXHJcblx0XHRcdHNlY3VyZTogcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09IFwicHJvZHVjdGlvblwiLFxyXG5cdFx0fSxcclxuXHR9LFxyXG5cdGF1dGhvcml6YXRpb25QYXJhbWV0ZXJzOiB7XHJcblx0XHRzY29wZTogcHJvY2Vzcy5lbnYuQVVUSDBfU0NPUEUsXHJcblx0XHRhdWRpZW5jZTogcHJvY2Vzcy5lbnYuQVVUSDBfQVVESUVOQ0UsXHJcblx0fSxcclxufSk7XHJcbiJdLCJuYW1lcyI6WyJBdXRoMENsaWVudCIsImF1dGgwIiwic2Vzc2lvbiIsInJvbGxpbmciLCJjb29raWUiLCJuYW1lIiwicGF0aCIsInNhbWVTaXRlIiwic2VjdXJlIiwicHJvY2VzcyIsImF1dGhvcml6YXRpb25QYXJhbWV0ZXJzIiwic2NvcGUiLCJlbnYiLCJBVVRIMF9TQ09QRSIsImF1ZGllbmNlIiwiQVVUSDBfQVVESUVOQ0UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth0.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsubnet-verification%2Fuser%2Fverify%2Froute&page=%2Fapi%2Fsubnet-verification%2Fuser%2Fverify%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsubnet-verification%2Fuser%2Fverify%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsubnet-verification%2Fuser%2Fverify%2Froute&page=%2Fapi%2Fsubnet-verification%2Fuser%2Fverify%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsubnet-verification%2Fuser%2Fverify%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_benji_Desktop_G_PROG_Nicolas_btdash_ecosystem_btdashs_app_api_subnet_verification_user_verify_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/subnet-verification/user/verify/route.ts */ \"(rsc)/./app/api/subnet-verification/user/verify/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/subnet-verification/user/verify/route\",\n        pathname: \"/api/subnet-verification/user/verify\",\n        filename: \"route\",\n        bundlePath: \"app/api/subnet-verification/user/verify/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\api\\\\subnet-verification\\\\user\\\\verify\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_benji_Desktop_G_PROG_Nicolas_btdash_ecosystem_btdashs_app_api_subnet_verification_user_verify_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsubnet-verification%2Fuser%2Fverify%2Froute&page=%2Fapi%2Fsubnet-verification%2Fuser%2Fverify%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsubnet-verification%2Fuser%2Fverify%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:https":
/*!*****************************!*\
  !*** external "node:https" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:https");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/jose","vendor-chunks/oauth4webapi","vendor-chunks/@auth0","vendor-chunks/@edge-runtime","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsubnet-verification%2Fuser%2Fverify%2Froute&page=%2Fapi%2Fsubnet-verification%2Fuser%2Fverify%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsubnet-verification%2Fuser%2Fverify%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();