"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/subnets/[id]/page",{

/***/ "(app-pages-browser)/./components/subnets/subnet-profile.tsx":
/*!***********************************************!*\
  !*** ./components/subnets/subnet-profile.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SubnetProfile)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Book_Github_Globe_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Book,Github,Globe,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book.js\");\n/* harmony import */ var _barrel_optimize_names_Book_Github_Globe_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Book,Github,Globe,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Book_Github_Globe_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Book,Github,Globe,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Book_Github_Globe_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Book,Github,Globe,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var swiper_modules__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! swiper/modules */ \"(app-pages-browser)/./node_modules/swiper/modules/index.mjs\");\n/* harmony import */ var swiper_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! swiper/react */ \"(app-pages-browser)/./node_modules/swiper/swiper-react.mjs\");\n/* harmony import */ var _components_ads_placements_ad_banner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ads-placements/ad-banner */ \"(app-pages-browser)/./components/ads-placements/ad-banner.tsx\");\n/* harmony import */ var _components_category_tag__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/category-tag */ \"(app-pages-browser)/./components/category-tag.tsx\");\n/* harmony import */ var _components_subnet_documentation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/subnet-documentation */ \"(app-pages-browser)/./components/subnet-documentation.tsx\");\n/* harmony import */ var _components_subnet_github_contribution_graph__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/subnet-github-contribution-graph */ \"(app-pages-browser)/./components/subnet-github-contribution-graph.tsx\");\n/* harmony import */ var _components_subnet_news__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/subnet-news */ \"(app-pages-browser)/./components/subnet-news.tsx\");\n/* harmony import */ var _components_subnet_team__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/subnet-team */ \"(app-pages-browser)/./components/subnet-team.tsx\");\n/* harmony import */ var _components_subnet_validators__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/subnet-validators */ \"(app-pages-browser)/./components/subnet-validators.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _public_tao_logo_svg__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/public/tao-logo.svg */ \"(app-pages-browser)/./public/tao-logo.svg\");\n/* harmony import */ var _components_subnets_subnet_relationship_chart__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/subnets/subnet-relationship-chart */ \"(app-pages-browser)/./components/subnets/subnet-relationship-chart.tsx\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var _subnet_applications__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../subnet-applications */ \"(app-pages-browser)/./components/subnet-applications.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Use custom API route instead of @auth0/nextjs-auth0/client to avoid module export issues\n\n\n\n\n\n\n\n\n\n\n\n// Import the new chart component\n\n\n\n// Utility function to convert video URLs to embeddable format\nconst getEmbeddableVideoUrl = (url)=>{\n    if (!url) return url;\n    // YouTube URL conversion\n    if (url.includes(\"youtube.com/watch?v=\")) {\n        var _url_split_;\n        const videoId = (_url_split_ = url.split(\"v=\")[1]) === null || _url_split_ === void 0 ? void 0 : _url_split_.split(\"&\")[0];\n        return \"https://www.youtube.com/embed/\".concat(videoId);\n    }\n    if (url.includes(\"youtu.be/\")) {\n        var _url_split_1;\n        const videoId = (_url_split_1 = url.split(\"youtu.be/\")[1]) === null || _url_split_1 === void 0 ? void 0 : _url_split_1.split(\"?\")[0];\n        return \"https://www.youtube.com/embed/\".concat(videoId);\n    }\n    // Vimeo URL conversion\n    if (url.includes(\"vimeo.com/\")) {\n        var _url_split_2;\n        const videoId = (_url_split_2 = url.split(\"vimeo.com/\")[1]) === null || _url_split_2 === void 0 ? void 0 : _url_split_2.split(\"?\")[0];\n        return \"https://player.vimeo.com/video/\".concat(videoId);\n    }\n    // Return original URL if no conversion needed\n    return url;\n};\n// Utility function to detect video type from URL\nconst getVideoType = (url)=>{\n    if (!url) return \"\";\n    if (url.includes(\"youtube.com\") || url.includes(\"youtu.be\")) {\n        return \"youtube\";\n    }\n    if (url.includes(\"vimeo.com\")) {\n        return \"vimeo\";\n    }\n    return \"other\";\n};\nfunction SubnetProfile(param) {\n    let { subnet, metrics, subnetProfile, categories, news, products, jobs, events, companies } = param;\n    var _subnet_subnet_ids, _subnet_images, _metrics_github_contributions;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [userLoading, setUserLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [isVerified, setIsVerified] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [checkingVerification, setCheckingVerification] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // Fetch user data using custom API route\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"SubnetProfile.useEffect\": ()=>{\n            const fetchUser = {\n                \"SubnetProfile.useEffect.fetchUser\": async ()=>{\n                    try {\n                        const response = await fetch(\"/api/user/me\");\n                        if (response.ok) {\n                            const userData = await response.json();\n                            setUser(userData);\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching user:\", error);\n                    } finally{\n                        setUserLoading(false);\n                    }\n                }\n            }[\"SubnetProfile.useEffect.fetchUser\"];\n            fetchUser();\n        }\n    }[\"SubnetProfile.useEffect\"], []);\n    const netuid = subnet.netuid;\n    // Check if current user has already verified this subnet\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"SubnetProfile.useEffect\": ()=>{\n            const checkVerificationStatus = {\n                \"SubnetProfile.useEffect.checkVerificationStatus\": async ()=>{\n                    if (!user) return;\n                    setCheckingVerification(true);\n                    try {\n                        const response = await fetch(\"/api/subnet-verification/user/subnets\");\n                        if (response.ok) {\n                            const data = await response.json();\n                            const verifiedSubnets = data.data || [];\n                            setIsVerified(verifiedSubnets.some({\n                                \"SubnetProfile.useEffect.checkVerificationStatus\": (vs)=>vs.netuid === netuid\n                            }[\"SubnetProfile.useEffect.checkVerificationStatus\"]));\n                        }\n                    } catch (error) {\n                        console.error(\"Error checking verification status:\", error);\n                    } finally{\n                        setCheckingVerification(false);\n                    }\n                }\n            }[\"SubnetProfile.useEffect.checkVerificationStatus\"];\n            checkVerificationStatus();\n        }\n    }[\"SubnetProfile.useEffect\"], [\n        user,\n        netuid\n    ]);\n    const handleClaimOwnership = ()=>{\n        // Navigate to profile page with subnet pre-selected\n        router.push(\"/profile?tab=subnet-verification&netuid=\".concat(netuid));\n    };\n    var _companies_length, _products_length, _events_length, _jobs_length, _categories_length, _metrics_validators_count, _news_length, _subnet_subnet_ids_length;\n    /* const images = subnet.images?.length\r\n    ? subnet.images\r\n    : [\r\n        \"https://via.placeholder.com/800x400?text=Image+1\",\r\n        \"https://via.placeholder.com/800x400?text=Image+2\",\r\n        \"https://via.placeholder.com/800x400?text=Image+3\",\r\n      ]; */ const data = {\n        companyCount: (_companies_length = companies === null || companies === void 0 ? void 0 : companies.length) !== null && _companies_length !== void 0 ? _companies_length : 0,\n        productCount: (_products_length = products === null || products === void 0 ? void 0 : products.length) !== null && _products_length !== void 0 ? _products_length : 0,\n        eventCount: (_events_length = events === null || events === void 0 ? void 0 : events.length) !== null && _events_length !== void 0 ? _events_length : 0,\n        jobCount: (_jobs_length = jobs === null || jobs === void 0 ? void 0 : jobs.length) !== null && _jobs_length !== void 0 ? _jobs_length : 0,\n        categoryCount: (_categories_length = categories === null || categories === void 0 ? void 0 : categories.length) !== null && _categories_length !== void 0 ? _categories_length : 0,\n        validatorCount: (_metrics_validators_count = metrics === null || metrics === void 0 ? void 0 : metrics.validators_count) !== null && _metrics_validators_count !== void 0 ? _metrics_validators_count : 0,\n        newsCount: (_news_length = news.length) !== null && _news_length !== void 0 ? _news_length : 0,\n        subnetCount: (_subnet_subnet_ids_length = subnet === null || subnet === void 0 ? void 0 : (_subnet_subnet_ids = subnet.subnet_ids) === null || _subnet_subnet_ids === void 0 ? void 0 : _subnet_subnet_ids.length) !== null && _subnet_subnet_ids_length !== void 0 ? _subnet_subnet_ids_length : 0\n    };\n    var _metrics_validators_count1, _metrics_emission;\n    const metricsCards = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_14__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_14__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_14__.CardTitle, {\n                            children: \"Price\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_14__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-3xl font-bold\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    src: _public_tao_logo_svg__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                                    alt: \"TAO\",\n                                    width: 24,\n                                    height: 24,\n                                    className: \"inline-block pr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 7\n                                }, this),\n                                (metrics === null || metrics === void 0 ? void 0 : metrics.alpha_price_tao) != null && !isNaN(metrics.alpha_price_tao) ? Number(metrics.alpha_price_tao) < 0.01 ? Number(metrics.alpha_price_tao).toFixed(3) : Number(metrics.alpha_price_tao).toFixed(2) : \"0.00\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                lineNumber: 171,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_14__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_14__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_14__.CardTitle, {\n                            children: \"Validators\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_14__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-3xl font-bold\",\n                            children: (_metrics_validators_count1 = metrics === null || metrics === void 0 ? void 0 : metrics.validators_count) !== null && _metrics_validators_count1 !== void 0 ? _metrics_validators_count1 : 0\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                lineNumber: 187,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_14__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_14__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_14__.CardTitle, {\n                            children: \"Emission\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_14__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-3xl font-bold\",\n                            children: [\n                                (((_metrics_emission = metrics === null || metrics === void 0 ? void 0 : metrics.emission) !== null && _metrics_emission !== void 0 ? _metrics_emission : 0) / 1e7).toFixed(2),\n                                \"%\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                lineNumber: 196,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_14__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_14__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_14__.CardTitle, {\n                            children: \"Miners\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_14__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-3xl font-bold \".concat(subnet.active_miners <= 5 ? \"text-red-500\" : subnet.active_miners <= 15 ? \"text-orange-500\" : \"text-green-500\"),\n                            children: subnet.active_miners || 0\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                lineNumber: 205,\n                columnNumber: 4\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n        lineNumber: 170,\n        columnNumber: 3\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-8 px-6 sm:px-8 lg:px-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full mb-8\",\n                    style: {\n                        minHeight: \"90px\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_placements_ad_banner__WEBPACK_IMPORTED_MODULE_6__.AdBanner, {\n                        variant: \"horizontal\",\n                        className: \"mx-auto\",\n                        adSlot: \"7230250579\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 6\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 5\n                }, this),\n                subnet.banner_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8 w-full h-32 md:h-40 lg:h-52 rounded-lg overflow-hidden shadow-lg bg-gray-100 dark:bg-gray-800 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: subnet.banner_url,\n                        alt: \"\".concat(subnet.name, \" banner\"),\n                        className: \"max-w-full max-h-full object-contain\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 7\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 6\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8 grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 rounded-full bg-primary flex items-center justify-center text-black text-3xl font-bold\",\n                                            children: subnet.subnet_symbol || subnet.name.charAt(0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold\",\n                                                    children: subnet.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: [\n                                                        \"Subnet ID: \",\n                                                        netuid\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: [\n                                                        \"Coldkey:\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"https://taostats.io/account/\".concat(subnet.sub_address_pkey, \"/?utm_source=dynamictoamarketcap&utm_medium=referral&utm_campaign=subnet_profile\"),\n                                                            target: \"_blank\",\n                                                            rel: \"noopener noreferrer\",\n                                                            className: \"text-xs text-muted-foreground underline hover:text-primary\",\n                                                            children: subnet.sub_address_pkey\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 10\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 flex flex-wrap gap-2 text-muted-foreground\",\n                                                    children: categories && categories.length > 0 ? categories.map((category, id)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_category_tag__WEBPACK_IMPORTED_MODULE_7__.CategoryTag, {\n                                                            category: category.name\n                                                        }, id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 13\n                                                        }, this)) : null\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 9\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_19__.Markdown, {\n                                        children: subnet.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-4 mb-8\",\n                                    children: [\n                                        subnet.white_paper ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                            asChild: true,\n                                            size: \"sm\",\n                                            className: \"gap-2\",\n                                            variant: \"default\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: subnet.white_paper,\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 11\n                                                    }, this),\n                                                    \"Read White Paper\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 10\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 9\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                            size: \"sm\",\n                                            className: \"gap-2\",\n                                            disabled: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 10\n                                                }, this),\n                                                \"White Paper Unavailable\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 9\n                                        }, this),\n                                        user && !userLoading && !isVerified && !checkingVerification && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                            onClick: handleClaimOwnership,\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            className: \"gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 10\n                                                }, this),\n                                                \"Claim Subnet Ownership\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 9\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 7\n                                }, this),\n                                subnet.images || subnet.main_video_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 overflow-hidden\",\n                                    children: metricsCards\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 8\n                                }, this) : null\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 6\n                        }, this),\n                        subnet.main_video_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative pb-[56.25%] h-0 overflow-hidden rounded-lg shadow-lg border border-slate-200 dark:border-slate-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                        className: \"absolute left-0 w-full h-full\",\n                                        src: getEmbeddableVideoUrl(subnet.main_video_url),\n                                        title: \"Subnet video\",\n                                        frameBorder: \"0\",\n                                        allow: \"autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n                                        allowFullScreen: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 9\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 8\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-4 pt-4\",\n                                    children: [\n                                        (subnet.website_perm || subnet.website) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.website_perm || subnet.website,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-6 w-6 text-blue-600 dark:text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 12\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 11\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors\",\n                                                            children: \"Official Website\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 12\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: (()=>{\n                                                                const websiteUrl = subnet.website_perm || subnet.website;\n                                                                return websiteUrl ? \"\".concat(websiteUrl.replace(/^https?:\\/\\/(www\\.)?/, \"\").slice(0, 30)).concat(websiteUrl.replace(/^https?:\\/\\/(www\\.)?/, \"\").length > 30 ? \"...\" : \"\") : \"\";\n                                                            })()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 12\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 11\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 10\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.github_repo || \"https://github.com/example/subnet-repo\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-purple-50 dark:bg-purple-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"h-6 w-6 text-purple-600 dark:text-purple-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors\",\n                                                            children: \"GitHub Repository\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: subnet.github_repo ? \"\".concat(subnet.github_repo.replace(\"https://github.com/\", \"\").slice(0, 30)).concat(subnet.github_repo.replace(\"https://github.com/\", \"\").length > 40 ? \"...\" : \"\") : \"github.com/example/subnet-repo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 9\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 8\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 7\n                        }, this) : !subnet.main_video_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                subnet.images ? null : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"gap-2 overflow-hidden\",\n                                    children: metricsCards\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 32\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"rounded-lg overflow-hidden shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_5__.Swiper, {\n                                        modules: [\n                                            swiper_modules__WEBPACK_IMPORTED_MODULE_4__.Navigation,\n                                            swiper_modules__WEBPACK_IMPORTED_MODULE_4__.Pagination\n                                        ],\n                                        navigation: true,\n                                        pagination: {\n                                            clickable: true\n                                        },\n                                        spaceBetween: 10,\n                                        slidesPerView: 1,\n                                        className: \"w-full h-full\",\n                                        children: (_subnet_images = subnet.images) === null || _subnet_images === void 0 ? void 0 : _subnet_images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_5__.SwiperSlide, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: image,\n                                                    alt: \"Subnet Image \".concat(index + 1),\n                                                    width: 800,\n                                                    height: 400,\n                                                    className: \"w-full h-auto object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 12\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 11\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 9\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 8\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-4 pt-4\",\n                                    children: [\n                                        (subnet.website_perm || subnet.website) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.website_perm || subnet.website,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-6 w-6 text-blue-600 dark:text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 12\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 11\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors\",\n                                                            children: \"Official Website\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 422,\n                                                            columnNumber: 12\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: (()=>{\n                                                                const websiteUrl = subnet.website_perm || subnet.website;\n                                                                return websiteUrl ? \"\".concat(websiteUrl.replace(/^https?:\\/\\/(www\\.)?/, \"\").slice(0, 30)).concat(websiteUrl.replace(/^https?:\\/\\/(www\\.)?/, \"\").length > 30 ? \"...\" : \"\") : \"\";\n                                                            })()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 12\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 11\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 10\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.github_repo || \"https://github.com/example/subnet-repo\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-purple-50 dark:bg-purple-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"h-6 w-6 text-purple-600 dark:text-purple-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors\",\n                                                            children: \"GitHub Repository\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: subnet.github_repo ? \"\".concat(subnet.github_repo.replace(\"https://github.com/\", \"\").slice(0, 30)).concat(subnet.github_repo.replace(\"https://github.com/\", \"\").length > 40 ? \"...\" : \"\") : \"github.com/example/subnet-repo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 9\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 8\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 7\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \" gap-4 overflow-hidden max-[60px]\",\n                                    children: metricsCards\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 8\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-4 pt-4\",\n                                    children: [\n                                        (subnet.website || subnet.website_perm) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.website || subnet.website_perm,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-6 w-6 text-blue-600 dark:text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 12\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 11\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors\",\n                                                            children: \"Official Website\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 12\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: (()=>{\n                                                                const websiteUrl = subnet.website || subnet.website_perm;\n                                                                return websiteUrl ? \"\".concat(websiteUrl.replace(/^https?:\\/\\/(www\\.)?/, \"\").slice(0, 30)).concat(websiteUrl.replace(/^https?:\\/\\/(www\\.)?/, \"\").length > 30 ? \"...\" : \"\") : \"\";\n                                                            })()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 491,\n                                                            columnNumber: 12\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 11\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 10\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.github_repo || \"https://github.com/example/subnet-repo\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-purple-50 dark:bg-purple-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"h-6 w-6 text-purple-600 dark:text-purple-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors\",\n                                                            children: \"GitHub Repository\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: subnet.github_repo ? \"\".concat(subnet.github_repo.replace(\"https://github.com/\", \"\").slice(0, 30)).concat(subnet.github_repo.replace(\"https://github.com/\", \"\").length > 40 ? \"...\" : \"\") : \"github.com/example/subnet-repo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 523,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 519,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 9\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 476,\n                                    columnNumber: 8\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 474,\n                            columnNumber: 7\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-7 gap-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnet_github_contribution_graph__WEBPACK_IMPORTED_MODULE_9__.SubnetGithubContributionGraph, {\n                                className: \"h-[360px]\",\n                                contributions: (metrics === null || metrics === void 0 ? void 0 : (_metrics_github_contributions = metrics.github_contributions) === null || _metrics_github_contributions === void 0 ? void 0 : _metrics_github_contributions.data) || []\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                lineNumber: 546,\n                                columnNumber: 7\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 545,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-[360px] col-span-2 overflow-visible\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnets_subnet_relationship_chart__WEBPACK_IMPORTED_MODULE_17__.SubnetRelationshipChart, {\n                                subnetId: subnet.name,\n                                data: data,\n                                className: \"h-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                lineNumber: 554,\n                                columnNumber: 7\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 553,\n                            columnNumber: 6\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 543,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-0 mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_subnet_applications__WEBPACK_IMPORTED_MODULE_18__.SubnetApplications, {\n                        products: products\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 560,\n                        columnNumber: 6\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 559,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_15__.Tabs, {\n                    defaultValue: \"overview\",\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_15__.TabsList, {\n                            className: \"flex flex-wrap\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_15__.TabsTrigger, {\n                                    value: \"overview\",\n                                    children: \"Overview\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 566,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_15__.TabsTrigger, {\n                                    value: \"team\",\n                                    children: \"Team\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 567,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_15__.TabsTrigger, {\n                                    value: \"documentation\",\n                                    children: \"Documentation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_15__.TabsTrigger, {\n                                    value: \"validators\",\n                                    children: \"Validators\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 569,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_15__.TabsTrigger, {\n                                    value: \"news\",\n                                    children: \"News\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 570,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 565,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-1 gap-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_15__.TabsContent, {\n                                        value: \"overview\",\n                                        className: \"space-y-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_14__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_14__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_14__.CardTitle, {\n                                                        className: \"text-lg\",\n                                                        children: \"Key Features\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 578,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 577,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_14__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid md:grid-cols-2 gap-4\",\n                                                        children: subnet.key_features && subnet.key_features.length > 0 ? (()=>{\n                                                            // Handle different key_features formats\n                                                            let featuresArray = [];\n                                                            if (Array.isArray(subnet.key_features)) {\n                                                                // Check if it's a nested array format [[{title, description}]]\n                                                                if (Array.isArray(subnet.key_features[0])) {\n                                                                    featuresArray = subnet.key_features[0];\n                                                                } else {\n                                                                    // Direct array format [{title, description}]\n                                                                    featuresArray = subnet.key_features;\n                                                                }\n                                                            }\n                                                            return featuresArray.map((feature, id)=>feature && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2 pb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-medium\",\n                                                                            children: feature.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                                            lineNumber: 601,\n                                                                            columnNumber: 18\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-muted-foreground\",\n                                                                            children: feature.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                                            lineNumber: 602,\n                                                                            columnNumber: 18\n                                                                        }, this)\n                                                                    ]\n                                                                }, id, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                                    lineNumber: 600,\n                                                                    columnNumber: 17\n                                                                }, this));\n                                                        })() : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: \"No key features available.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 610,\n                                                            columnNumber: 13\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 576,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 575,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_15__.TabsContent, {\n                                        value: \"team\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnet_team__WEBPACK_IMPORTED_MODULE_11__.SubnetTeam, {\n                                            subnet: subnet\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 618,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_15__.TabsContent, {\n                                        value: \"documentation\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnet_documentation__WEBPACK_IMPORTED_MODULE_8__.SubnetDocumentation, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 620,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_15__.TabsContent, {\n                                        value: \"validators\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnet_validators__WEBPACK_IMPORTED_MODULE_12__.SubnetValidators, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 624,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 623,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_15__.TabsContent, {\n                                        value: \"news\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnet_news__WEBPACK_IMPORTED_MODULE_10__.SubnetNews, {\n                                            news: news\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 627,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 8\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                lineNumber: 574,\n                                columnNumber: 7\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 573,\n                            columnNumber: 6\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 564,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 mt-8 mb-8 min-h-[90px] w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_placements_ad_banner__WEBPACK_IMPORTED_MODULE_6__.AdBanner, {\n                        variant: \"horizontal\",\n                        className: \"w-full h-full\",\n                        adSlot: \"2282987717\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 635,\n                        columnNumber: 6\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 634,\n                    columnNumber: 5\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n            lineNumber: 228,\n            columnNumber: 4\n        }, this)\n    }, void 0, false);\n}\n_s(SubnetProfile, \"ug9ZAVSqz4lm27f50mw3SFMeSBE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = SubnetProfile;\nvar _c;\n$RefreshReg$(_c, \"SubnetProfile\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/subnets/subnet-profile.tsx\n"));

/***/ })

});