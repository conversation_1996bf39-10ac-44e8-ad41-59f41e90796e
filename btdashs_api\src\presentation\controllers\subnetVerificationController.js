// src/presentation/controllers/subnetVerificationController.js

const SubnetVerificationService = require("../../application/services/SubnetVerificationService");
const {
	sendSuccess,
	sendError,
	sendUnauthorized,
	sendForbidden,
	sendInternalError,
} = require("../../utils/responseWrapper");

// Helper function for bad requests
const sendBadRequest = (res, message) => sendError(res, message, null, 400);
const logger = require("../../../logger");

const subnetVerificationService = new SubnetVerificationService();

/**
 * Generate a challenge for subnet ownership verification
 * POST /api/subnet-verification/challenge
 */
const generateChallenge = async (req, res) => {
	try {
		// Extract Auth0 ID from JWT token
		const auth0_id = req.auth?.sub;
		if (!auth0_id) {
			return sendUnauthorized(res, "Authentication required");
		}

		// Get user from database using Auth0 ID
		const UserService = require("../../application/services/UserService");
		const user = await UserService.getUserByAuth0Id(auth0_id);
		if (!user) {
			return sendUnauthorized(res, "User not found");
		}

		const userId = user.id;

		const { netuid, companyId } = req.body;

		// Validate input
		if (!netuid || !companyId) {
			return sendBadRequest(res, "netuid and companyId are required");
		}

		if (!Number.isInteger(netuid) || !Number.isInteger(companyId)) {
			return sendBadRequest(res, "netuid and companyId must be integers");
		}

		// Check if user has permission to modify the company
		const db = require("../../infrastructure/database/knex");
		const userCompany = await db("dtm_base.user_company")
			.where({
				user_id: userId,
				company_id: companyId,
			})
			.first();

		if (!userCompany || !["owner", "admin"].includes(userCompany.role)) {
			return sendForbidden(res, "Insufficient permissions to verify subnets for this company");
		}

		// Check if subnet exists
		const subnet = await db("dtm_base.subnets").where({ netuid }).first();

		if (!subnet) {
			return sendBadRequest(res, "Subnet not found");
		}

		// Check if this specific user already verified this subnet for this company
		const existingVerification = await db("dtm_base.verified_subnet_ownership")
			.where({
				netuid,
				company_id: companyId,
				user_id: userId,
				is_active: true,
			})
			.first();

		if (existingVerification) {
			return sendBadRequest(res, "You have already verified this subnet for this company");
		}

		// Generate challenge
		const challenge = await subnetVerificationService.generateChallenge(userId, netuid, companyId);

		logger.info("Challenge generated successfully", {
			userId,
			netuid,
			companyId,
			challengeId: challenge.challengeId,
		});

		return sendSuccess(res, challenge, "Challenge generated successfully");
	} catch (error) {
		logger.error("Error generating challenge:", {
			error: error.message,
			stack: error.stack,
			userId: req.auth?.sub,
			body: req.body,
		});

		if (error.message.includes("Rate limit exceeded")) {
			return sendError(res, error.message, 429);
		}

		return sendError(res, "Failed to generate challenge");
	}
};

/**
 * Verify subnet ownership using cryptographic signature
 * POST /api/subnet-verification/verify
 */
const verifyOwnership = async (req, res) => {
	try {
		// Extract Auth0 ID from JWT token
		const auth0_id = req.auth?.sub;
		if (!auth0_id) {
			return sendUnauthorized(res, "Authentication required");
		}

		// Get user from database using Auth0 ID
		const UserService = require("../../application/services/UserService");
		const user = await UserService.getUserByAuth0Id(auth0_id);
		if (!user) {
			return sendUnauthorized(res, "User not found");
		}

		const userId = user.id;

		const { challengeId, signature, signerAddress, companyId } = req.body;

		// Validate input
		if (!challengeId || !signature || !signerAddress || !companyId) {
			return sendBadRequest(res, "challengeId, signature, signerAddress, and companyId are required");
		}

		if (!Number.isInteger(challengeId) || !Number.isInteger(companyId)) {
			return sendBadRequest(res, "challengeId and companyId must be integers");
		}

		// Validate signature format (should be hex)
		if (!/^0x[a-fA-F0-9]+$/.test(signature)) {
			return sendBadRequest(res, "Invalid signature format");
		}

		// Validate address format (basic check)
		if (!signerAddress || signerAddress.length < 10) {
			return sendBadRequest(res, "Invalid signer address format");
		}

		// Check if user has permission to modify the company
		const db = require("../../infrastructure/database/knex");
		const userCompany = await db("dtm_base.user_company")
			.where({
				user_id: userId,
				company_id: companyId,
			})
			.first();

		if (!userCompany || !["owner", "admin"].includes(userCompany.role)) {
			return sendForbidden(res, "Insufficient permissions to verify subnets for this company");
		}

		// Verify ownership
		const verificationResult = await subnetVerificationService.verifyOwnership(
			userId,
			challengeId,
			signature,
			signerAddress,
			companyId
		);

		logger.info("Subnet ownership verified successfully", {
			userId,
			companyId,
			verificationId: verificationResult.verificationId,
			netuid: verificationResult.netuid,
		});

		return sendSuccess(res, verificationResult, "Subnet ownership verified successfully");
	} catch (error) {
		logger.error("Error verifying ownership:", {
			error: error.message,
			stack: error.stack,
			userId: req.auth?.sub,
			body: req.body,
		});

		// Handle specific error types
		if (error.message.includes("Invalid or expired challenge")) {
			return sendBadRequest(res, "Invalid or expired challenge");
		}

		if (error.message.includes("Challenge has expired")) {
			return sendBadRequest(res, "Challenge has expired. Please generate a new challenge.");
		}

		if (error.message.includes("Invalid signature")) {
			return sendBadRequest(res, "Invalid signature. Please check your wallet signature.");
		}

		if (error.message.includes("not the subnet owner")) {
			return sendBadRequest(res, "The signing address is not the owner of this subnet.");
		}

		return sendError(res, "Failed to verify subnet ownership");
	}
};

/**
 * Get verified subnets for a company
 * GET /api/subnet-verification/company/:companyId
 */
const getVerifiedSubnets = async (req, res) => {
	try {
		const { companyId } = req.params;

		// Extract Auth0 ID from JWT token
		const auth0_id = req.auth?.sub;
		if (!auth0_id) {
			return sendUnauthorized(res, "Authentication required");
		}

		// Get user from database using Auth0 ID
		const UserService = require("../../application/services/UserService");
		const user = await UserService.getUserByAuth0Id(auth0_id);
		if (!user) {
			return sendUnauthorized(res, "User not found");
		}

		const userId = user.id;

		if (!companyId || !Number.isInteger(parseInt(companyId))) {
			return sendBadRequest(res, "Valid companyId is required");
		}

		const companyIdInt = parseInt(companyId);

		// Check if user has access to this company
		const db = require("../../infrastructure/database/knex");
		const userCompany = await db("dtm_base.user_company")
			.where({
				user_id: userId,
				company_id: companyIdInt,
			})
			.first();

		if (!userCompany) {
			return sendForbidden(res, "Access denied to this company");
		}

		// Get verified subnets
		const verifiedSubnets = await subnetVerificationService.getVerifiedSubnets(companyIdInt);

		return sendSuccess(res, verifiedSubnets, "Verified subnets retrieved successfully");
	} catch (error) {
		logger.error("Error getting verified subnets:", {
			error: error.message,
			stack: error.stack,
			userId: req.auth?.sub,
			companyId: req.params.companyId,
		});

		return sendError(res, "Failed to retrieve verified subnets");
	}
};

/**
 * Remove subnet verification
 * DELETE /api/subnet-verification/company/:companyId/subnet/:netuid
 */
const removeVerification = async (req, res) => {
	try {
		const { companyId, netuid } = req.params;

		// Extract Auth0 ID from JWT token
		const auth0_id = req.auth?.sub;
		if (!auth0_id) {
			return sendUnauthorized(res, "Authentication required");
		}

		// Get user from database using Auth0 ID
		const UserService = require("../../application/services/UserService");
		const user = await UserService.getUserByAuth0Id(auth0_id);
		if (!user) {
			return sendUnauthorized(res, "User not found");
		}

		const userId = user.id;

		if (!companyId || !netuid || !Number.isInteger(parseInt(companyId)) || !Number.isInteger(parseInt(netuid))) {
			return sendBadRequest(res, "Valid companyId and netuid are required");
		}

		const companyIdInt = parseInt(companyId);
		const netuidInt = parseInt(netuid);

		// Remove verification
		const success = await subnetVerificationService.removeVerification(userId, companyIdInt, netuidInt);

		if (success) {
			logger.info("Subnet verification removed successfully", {
				userId,
				companyId: companyIdInt,
				netuid: netuidInt,
			});

			return sendSuccess(res, { removed: true }, "Subnet verification removed successfully");
		} else {
			return sendError(res, "Failed to remove subnet verification");
		}
	} catch (error) {
		logger.error("Error removing verification:", {
			error: error.message,
			stack: error.stack,
			userId: req.auth?.sub,
			params: req.params,
		});

		if (error.message.includes("Insufficient permissions")) {
			return sendForbidden(res, "Insufficient permissions to remove this verification");
		}

		if (error.message.includes("Verification not found")) {
			return sendBadRequest(res, "Verification not found");
		}

		return sendError(res, "Failed to remove subnet verification");
	}
};

/**
 * Generate challenge for user-only subnet verification
 */
const generateUserChallenge = async (req, res) => {
	try {
		// Extract Auth0 ID from JWT token
		const auth0_id = req.auth?.sub;
		if (!auth0_id) {
			return sendUnauthorized(res, "Authentication required");
		}

		// Get user from database using Auth0 ID
		const UserService = require("../../application/services/UserService");
		const user = await UserService.getUserByAuth0Id(auth0_id);
		if (!user) {
			return sendUnauthorized(res, "User not found");
		}

		const userId = user.id;

		const { netuid } = req.body;

		// Validate input
		if (!netuid || !Number.isInteger(netuid)) {
			return sendBadRequest(res, "netuid must be an integer");
		}

		// Check if subnet exists
		const db = require("../../infrastructure/database/knex");
		const subnet = await db("dtm_base.subnets").where({ netuid }).first();

		if (!subnet) {
			return sendBadRequest(res, "Subnet not found");
		}

		// Check if this user already verified this subnet (user-only)
		const existingVerification = await db("dtm_base.verified_subnet_ownership")
			.where({
				netuid,
				user_id: userId,
				company_id: null,
				is_active: true,
			})
			.first();

		if (existingVerification) {
			return sendBadRequest(res, "You have already verified this subnet");
		}

		// Generate challenge (no companyId)
		const challenge = await subnetVerificationService.generateChallenge(userId, netuid, null);

		return sendSuccess(res, challenge, "Challenge generated successfully");
	} catch (error) {
		logger.error("Error generating user challenge:", error);
		return sendInternalError(res, "Failed to generate challenge");
	}
};

/**
 * Verify user-only subnet ownership
 */
const verifyUserOwnership = async (req, res) => {
	try {
		// Extract Auth0 ID from JWT token
		const auth0_id = req.auth?.sub;
		if (!auth0_id) {
			return sendUnauthorized(res, "Authentication required");
		}

		// Get user from database using Auth0 ID
		const UserService = require("../../application/services/UserService");
		const user = await UserService.getUserByAuth0Id(auth0_id);
		if (!user) {
			return sendUnauthorized(res, "User not found");
		}

		const userId = user.id;

		const { challengeId, signature, signerAddress } = req.body;

		// Validate input
		if (!challengeId || !signature || !signerAddress) {
			return sendBadRequest(res, "challengeId, signature, and signerAddress are required");
		}

		// Validate signature format
		if (!/^0x[a-fA-F0-9]+$/.test(signature)) {
			return sendBadRequest(res, "Invalid signature format");
		}

		// Validate address format
		if (!signerAddress || signerAddress.length < 10) {
			return sendBadRequest(res, "Invalid signer address format");
		}

		// Verify ownership (no companyId)
		const result = await subnetVerificationService.verifyOwnership(
			userId,
			challengeId,
			signature,
			signerAddress,
			null
		);

		return sendSuccess(res, result, "Ownership verified successfully");
	} catch (error) {
		logger.error("Error verifying user ownership:", error);
		return sendInternalError(res, error.message || "Failed to verify ownership");
	}
};

/**
 * Get verified subnets for current user
 */
const getUserVerifiedSubnets = async (req, res) => {
	try {
		// Extract Auth0 ID from JWT token
		const auth0_id = req.auth?.sub;
		if (!auth0_id) {
			return sendUnauthorized(res, "Authentication required");
		}

		// Get user from database using Auth0 ID
		const UserService = require("../../application/services/UserService");
		const user = await UserService.getUserByAuth0Id(auth0_id);
		if (!user) {
			return sendUnauthorized(res, "User not found");
		}

		const userId = user.id;

		const verifiedSubnets = await subnetVerificationService.getUserVerifiedSubnets(userId);

		return sendSuccess(res, verifiedSubnets, "Verified subnets retrieved successfully");
	} catch (error) {
		logger.error("Error getting user verified subnets:", error);
		return sendInternalError(res, "Failed to get verified subnets");
	}
};

/**
 * Remove user-only subnet verification
 */
const removeUserVerification = async (req, res) => {
	try {
		// Extract Auth0 ID from JWT token
		const auth0_id = req.auth?.sub;
		if (!auth0_id) {
			return sendUnauthorized(res, "Authentication required");
		}

		// Get user from database using Auth0 ID
		const UserService = require("../../application/services/UserService");
		const user = await UserService.getUserByAuth0Id(auth0_id);
		if (!user) {
			return sendUnauthorized(res, "User not found");
		}

		const userId = user.id;

		const { netuid } = req.params;

		// Validate netuid
		const netuidInt = parseInt(netuid);
		if (!Number.isInteger(netuidInt)) {
			return sendBadRequest(res, "Invalid netuid");
		}

		// Remove verification (no companyId)
		await subnetVerificationService.removeVerification(userId, null, netuidInt);

		return sendSuccess(res, null, "Verification removed successfully");
	} catch (error) {
		logger.error("Error removing user verification:", error);
		return sendInternalError(res, error.message || "Failed to remove verification");
	}
};

module.exports = {
	generateChallenge,
	verifyOwnership,
	getVerifiedSubnets,
	removeVerification,
	generateUserChallenge,
	verifyUserOwnership,
	getUserVerifiedSubnets,
	removeUserVerification,
};
