-- Migration: Fix subnet ownership constraint to prevent multiple users from verifying same subnet
-- This ensures only one user can prove ownership of each subnet

BEGIN;

-- Drop the existing unique constraint on (netuid, company_id)
ALTER TABLE dtm_base.verified_subnet_ownership 
DROP CONSTRAINT IF EXISTS verified_subnet_ownership_netuid_company_id_key;

-- Add a unique constraint on netuid only to prevent multiple users from verifying the same subnet
ALTER TABLE dtm_base.verified_subnet_ownership 
ADD CONSTRAINT verified_subnet_ownership_netuid_unique UNIQUE (netuid);

-- Create an index for better query performance on active verifications
CREATE INDEX IF NOT EXISTS idx_verified_subnet_ownership_active 
ON dtm_base.verified_subnet_ownership (netuid, is_active) 
WHERE is_active = true;

COMMIT;
