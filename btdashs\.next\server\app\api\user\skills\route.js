/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/user/skills/route";
exports.ids = ["app/api/user/skills/route"];
exports.modules = {

/***/ "(rsc)/./app/api/user/skills/route.ts":
/*!**************************************!*\
  !*** ./app/api/user/skills/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth0__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../lib/auth0 */ \"(rsc)/./lib/auth0.ts\");\n// app/api/user/skills/route.ts\n\n\nconst GET = async function getUserSkills(request) {\n    try {\n        const { token: accessToken } = await _lib_auth0__WEBPACK_IMPORTED_MODULE_1__.auth0.getAccessToken();\n        if (!accessToken) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/api/auth/login\", request.url));\n        }\n        const response = await fetch(`${process.env.API_BASE_URL}/user/skills`, {\n            headers: {\n                Authorization: `Bearer ${accessToken}`\n            }\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: error.message\n            }, {\n                status: response.status\n            });\n        }\n        const skills = await response.json();\n        // Return standardized format that fetchWithFallback expects\n        if (skills.data) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: skills.data\n            });\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: skills\n            });\n        }\n    } catch (error) {\n        console.error(\"Error fetching skills:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Server error\"\n        }, {\n            status: 500\n        });\n    }\n};\n// Add a new skill with level\nconst POST = async function addUserSkill(request) {\n    try {\n        const { token: accessToken } = await _lib_auth0__WEBPACK_IMPORTED_MODULE_1__.auth0.getAccessToken();\n        if (!accessToken) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/api/auth/login\", request.url));\n        }\n        const { skill_id, level } = await request.json();\n        if (!skill_id || !level) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Skill ID and Level are required\"\n            }, {\n                status: 400\n            });\n        }\n        const response = await fetch(`${process.env.API_BASE_URL}/user/skills`, {\n            method: \"POST\",\n            headers: {\n                Authorization: `Bearer ${accessToken}`,\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                skill_id,\n                level,\n                endorsements: 0\n            })\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: error.message\n            }, {\n                status: response.status\n            });\n        }\n        const skill = await response.json();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            data: skill\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Error adding skill:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Server error\"\n        }, {\n            status: 500\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL3VzZXIvc2tpbGxzL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQSwrQkFBK0I7QUFFWTtBQUNHO0FBRXZDLE1BQU1FLE1BQU0sZUFBZUMsY0FBY0MsT0FBZ0I7SUFDL0QsSUFBSTtRQUNILE1BQU0sRUFBRUMsT0FBT0MsV0FBVyxFQUFFLEdBQUcsTUFBTUwsNkNBQUtBLENBQUNNLGNBQWM7UUFFekQsSUFBSSxDQUFDRCxhQUFhO1lBQ2pCLE9BQU9OLHFEQUFZQSxDQUFDUSxRQUFRLENBQUMsSUFBSUMsSUFBSSxtQkFBbUJMLFFBQVFNLEdBQUc7UUFDcEU7UUFFQSxNQUFNQyxXQUFXLE1BQU1DLE1BQU0sR0FBR0MsUUFBUUMsR0FBRyxDQUFDQyxZQUFZLENBQUMsWUFBWSxDQUFDLEVBQUU7WUFDdkVDLFNBQVM7Z0JBQ1JDLGVBQWUsQ0FBQyxPQUFPLEVBQUVYLGFBQWE7WUFDdkM7UUFDRDtRQUVBLElBQUksQ0FBQ0ssU0FBU08sRUFBRSxFQUFFO1lBQ2pCLE1BQU1DLFFBQVEsTUFBTVIsU0FBU1MsSUFBSTtZQUNqQyxPQUFPcEIscURBQVlBLENBQUNvQixJQUFJLENBQUM7Z0JBQUVELE9BQU9BLE1BQU1FLE9BQU87WUFBQyxHQUFHO2dCQUFFQyxRQUFRWCxTQUFTVyxNQUFNO1lBQUM7UUFDOUU7UUFFQSxNQUFNQyxTQUFTLE1BQU1aLFNBQVNTLElBQUk7UUFFbEMsNERBQTREO1FBQzVELElBQUlHLE9BQU9DLElBQUksRUFBRTtZQUNoQixPQUFPeEIscURBQVlBLENBQUNvQixJQUFJLENBQUM7Z0JBQUVLLFNBQVM7Z0JBQU1ELE1BQU1ELE9BQU9DLElBQUk7WUFBQztRQUM3RCxPQUFPO1lBQ04sT0FBT3hCLHFEQUFZQSxDQUFDb0IsSUFBSSxDQUFDO2dCQUFFSyxTQUFTO2dCQUFNRCxNQUFNRDtZQUFPO1FBQ3hEO0lBQ0QsRUFBRSxPQUFPSixPQUFPO1FBQ2ZPLFFBQVFQLEtBQUssQ0FBQywwQkFBMEJBO1FBQ3hDLE9BQU9uQixxREFBWUEsQ0FBQ29CLElBQUksQ0FBQztZQUFFRCxPQUFPO1FBQWUsR0FBRztZQUFFRyxRQUFRO1FBQUk7SUFDbkU7QUFDRCxFQUFFO0FBRUYsNkJBQTZCO0FBQ3RCLE1BQU1LLE9BQU8sZUFBZUMsYUFBYXhCLE9BQWdCO0lBQy9ELElBQUk7UUFDSCxNQUFNLEVBQUVDLE9BQU9DLFdBQVcsRUFBRSxHQUFHLE1BQU1MLDZDQUFLQSxDQUFDTSxjQUFjO1FBRXpELElBQUksQ0FBQ0QsYUFBYTtZQUNqQixPQUFPTixxREFBWUEsQ0FBQ1EsUUFBUSxDQUFDLElBQUlDLElBQUksbUJBQW1CTCxRQUFRTSxHQUFHO1FBQ3BFO1FBRUEsTUFBTSxFQUFFbUIsUUFBUSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNMUIsUUFBUWdCLElBQUk7UUFFOUMsSUFBSSxDQUFDUyxZQUFZLENBQUNDLE9BQU87WUFDeEIsT0FBTzlCLHFEQUFZQSxDQUFDb0IsSUFBSSxDQUFDO2dCQUFFRCxPQUFPO1lBQWtDLEdBQUc7Z0JBQUVHLFFBQVE7WUFBSTtRQUN0RjtRQUVBLE1BQU1YLFdBQVcsTUFBTUMsTUFBTSxHQUFHQyxRQUFRQyxHQUFHLENBQUNDLFlBQVksQ0FBQyxZQUFZLENBQUMsRUFBRTtZQUN2RWdCLFFBQVE7WUFDUmYsU0FBUztnQkFDUkMsZUFBZSxDQUFDLE9BQU8sRUFBRVgsYUFBYTtnQkFDdEMsZ0JBQWdCO1lBQ2pCO1lBQ0EwQixNQUFNQyxLQUFLQyxTQUFTLENBQUM7Z0JBQ3BCTDtnQkFDQUM7Z0JBQ0FLLGNBQWM7WUFDZjtRQUNEO1FBRUEsSUFBSSxDQUFDeEIsU0FBU08sRUFBRSxFQUFFO1lBQ2pCLE1BQU1DLFFBQVEsTUFBTVIsU0FBU1MsSUFBSTtZQUNqQyxPQUFPcEIscURBQVlBLENBQUNvQixJQUFJLENBQUM7Z0JBQUVELE9BQU9BLE1BQU1FLE9BQU87WUFBQyxHQUFHO2dCQUFFQyxRQUFRWCxTQUFTVyxNQUFNO1lBQUM7UUFDOUU7UUFFQSxNQUFNYyxRQUFRLE1BQU16QixTQUFTUyxJQUFJO1FBQ2pDLE9BQU9wQixxREFBWUEsQ0FBQ29CLElBQUksQ0FBQztZQUFFSSxNQUFNWTtRQUFNLEdBQUc7WUFBRWQsUUFBUTtRQUFJO0lBQ3pELEVBQUUsT0FBT0gsT0FBTztRQUNmTyxRQUFRUCxLQUFLLENBQUMsdUJBQXVCQTtRQUNyQyxPQUFPbkIscURBQVlBLENBQUNvQixJQUFJLENBQUM7WUFBRUQsT0FBTztRQUFlLEdBQUc7WUFBRUcsUUFBUTtRQUFJO0lBQ25FO0FBQ0QsRUFBRSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxhcHBcXGFwaVxcdXNlclxcc2tpbGxzXFxyb3V0ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBhcHAvYXBpL3VzZXIvc2tpbGxzL3JvdXRlLnRzXHJcblxyXG5pbXBvcnQgeyBOZXh0UmVzcG9uc2UgfSBmcm9tIFwibmV4dC9zZXJ2ZXJcIjtcclxuaW1wb3J0IHsgYXV0aDAgfSBmcm9tIFwiLi4vLi4vLi4vLi4vbGliL2F1dGgwXCI7XHJcblxyXG5leHBvcnQgY29uc3QgR0VUID0gYXN5bmMgZnVuY3Rpb24gZ2V0VXNlclNraWxscyhyZXF1ZXN0OiBSZXF1ZXN0KSB7XHJcblx0dHJ5IHtcclxuXHRcdGNvbnN0IHsgdG9rZW46IGFjY2Vzc1Rva2VuIH0gPSBhd2FpdCBhdXRoMC5nZXRBY2Nlc3NUb2tlbigpO1xyXG5cclxuXHRcdGlmICghYWNjZXNzVG9rZW4pIHtcclxuXHRcdFx0cmV0dXJuIE5leHRSZXNwb25zZS5yZWRpcmVjdChuZXcgVVJMKFwiL2FwaS9hdXRoL2xvZ2luXCIsIHJlcXVlc3QudXJsKSk7XHJcblx0XHR9XHJcblxyXG5cdFx0Y29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtwcm9jZXNzLmVudi5BUElfQkFTRV9VUkx9L3VzZXIvc2tpbGxzYCwge1xyXG5cdFx0XHRoZWFkZXJzOiB7XHJcblx0XHRcdFx0QXV0aG9yaXphdGlvbjogYEJlYXJlciAke2FjY2Vzc1Rva2VufWAsXHJcblx0XHRcdH0sXHJcblx0XHR9KTtcclxuXHJcblx0XHRpZiAoIXJlc3BvbnNlLm9rKSB7XHJcblx0XHRcdGNvbnN0IGVycm9yID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG5cdFx0XHRyZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogZXJyb3IubWVzc2FnZSB9LCB7IHN0YXR1czogcmVzcG9uc2Uuc3RhdHVzIH0pO1xyXG5cdFx0fVxyXG5cclxuXHRcdGNvbnN0IHNraWxscyA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuXHJcblx0XHQvLyBSZXR1cm4gc3RhbmRhcmRpemVkIGZvcm1hdCB0aGF0IGZldGNoV2l0aEZhbGxiYWNrIGV4cGVjdHNcclxuXHRcdGlmIChza2lsbHMuZGF0YSkge1xyXG5cdFx0XHRyZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBzdWNjZXNzOiB0cnVlLCBkYXRhOiBza2lsbHMuZGF0YSB9KTtcclxuXHRcdH0gZWxzZSB7XHJcblx0XHRcdHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IHN1Y2Nlc3M6IHRydWUsIGRhdGE6IHNraWxscyB9KTtcclxuXHRcdH1cclxuXHR9IGNhdGNoIChlcnJvcikge1xyXG5cdFx0Y29uc29sZS5lcnJvcihcIkVycm9yIGZldGNoaW5nIHNraWxsczpcIiwgZXJyb3IpO1xyXG5cdFx0cmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6IFwiU2VydmVyIGVycm9yXCIgfSwgeyBzdGF0dXM6IDUwMCB9KTtcclxuXHR9XHJcbn07XHJcblxyXG4vLyBBZGQgYSBuZXcgc2tpbGwgd2l0aCBsZXZlbFxyXG5leHBvcnQgY29uc3QgUE9TVCA9IGFzeW5jIGZ1bmN0aW9uIGFkZFVzZXJTa2lsbChyZXF1ZXN0OiBSZXF1ZXN0KSB7XHJcblx0dHJ5IHtcclxuXHRcdGNvbnN0IHsgdG9rZW46IGFjY2Vzc1Rva2VuIH0gPSBhd2FpdCBhdXRoMC5nZXRBY2Nlc3NUb2tlbigpO1xyXG5cclxuXHRcdGlmICghYWNjZXNzVG9rZW4pIHtcclxuXHRcdFx0cmV0dXJuIE5leHRSZXNwb25zZS5yZWRpcmVjdChuZXcgVVJMKFwiL2FwaS9hdXRoL2xvZ2luXCIsIHJlcXVlc3QudXJsKSk7XHJcblx0XHR9XHJcblxyXG5cdFx0Y29uc3QgeyBza2lsbF9pZCwgbGV2ZWwgfSA9IGF3YWl0IHJlcXVlc3QuanNvbigpO1xyXG5cclxuXHRcdGlmICghc2tpbGxfaWQgfHwgIWxldmVsKSB7XHJcblx0XHRcdHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiBcIlNraWxsIElEIGFuZCBMZXZlbCBhcmUgcmVxdWlyZWRcIiB9LCB7IHN0YXR1czogNDAwIH0pO1xyXG5cdFx0fVxyXG5cclxuXHRcdGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7cHJvY2Vzcy5lbnYuQVBJX0JBU0VfVVJMfS91c2VyL3NraWxsc2AsIHtcclxuXHRcdFx0bWV0aG9kOiBcIlBPU1RcIixcclxuXHRcdFx0aGVhZGVyczoge1xyXG5cdFx0XHRcdEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHthY2Nlc3NUb2tlbn1gLFxyXG5cdFx0XHRcdFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxyXG5cdFx0XHR9LFxyXG5cdFx0XHRib2R5OiBKU09OLnN0cmluZ2lmeSh7XHJcblx0XHRcdFx0c2tpbGxfaWQsXHJcblx0XHRcdFx0bGV2ZWwsXHJcblx0XHRcdFx0ZW5kb3JzZW1lbnRzOiAwLFxyXG5cdFx0XHR9KSxcclxuXHRcdH0pO1xyXG5cclxuXHRcdGlmICghcmVzcG9uc2Uub2spIHtcclxuXHRcdFx0Y29uc3QgZXJyb3IgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcblx0XHRcdHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiBlcnJvci5tZXNzYWdlIH0sIHsgc3RhdHVzOiByZXNwb25zZS5zdGF0dXMgfSk7XHJcblx0XHR9XHJcblxyXG5cdFx0Y29uc3Qgc2tpbGwgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcblx0XHRyZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBkYXRhOiBza2lsbCB9LCB7IHN0YXR1czogMjAxIH0pO1xyXG5cdH0gY2F0Y2ggKGVycm9yKSB7XHJcblx0XHRjb25zb2xlLmVycm9yKFwiRXJyb3IgYWRkaW5nIHNraWxsOlwiLCBlcnJvcik7XHJcblx0XHRyZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogXCJTZXJ2ZXIgZXJyb3JcIiB9LCB7IHN0YXR1czogNTAwIH0pO1xyXG5cdH1cclxufTtcclxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsImF1dGgwIiwiR0VUIiwiZ2V0VXNlclNraWxscyIsInJlcXVlc3QiLCJ0b2tlbiIsImFjY2Vzc1Rva2VuIiwiZ2V0QWNjZXNzVG9rZW4iLCJyZWRpcmVjdCIsIlVSTCIsInVybCIsInJlc3BvbnNlIiwiZmV0Y2giLCJwcm9jZXNzIiwiZW52IiwiQVBJX0JBU0VfVVJMIiwiaGVhZGVycyIsIkF1dGhvcml6YXRpb24iLCJvayIsImVycm9yIiwianNvbiIsIm1lc3NhZ2UiLCJzdGF0dXMiLCJza2lsbHMiLCJkYXRhIiwic3VjY2VzcyIsImNvbnNvbGUiLCJQT1NUIiwiYWRkVXNlclNraWxsIiwic2tpbGxfaWQiLCJsZXZlbCIsIm1ldGhvZCIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwiZW5kb3JzZW1lbnRzIiwic2tpbGwiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/api/user/skills/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth0.ts":
/*!**********************!*\
  !*** ./lib/auth0.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth0: () => (/* binding */ auth0)\n/* harmony export */ });\n/* harmony import */ var _auth0_nextjs_auth0_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth0/nextjs-auth0/server */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/index.js\");\n// lib/auth0.js\n\n// Initialize the Auth0 client\nconst auth0 = new _auth0_nextjs_auth0_server__WEBPACK_IMPORTED_MODULE_0__.Auth0Client({\n    session: {\n        rolling: true,\n        cookie: {\n            name: \"app_session\",\n            path: \"/\",\n            sameSite: \"lax\",\n            secure: \"development\" === \"production\"\n        }\n    },\n    authorizationParameters: {\n        scope: process.env.AUTH0_SCOPE,\n        audience: process.env.AUTH0_AUDIENCE\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvYXV0aDAudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxlQUFlO0FBRTBDO0FBRXpELDhCQUE4QjtBQUN2QixNQUFNQyxRQUFRLElBQUlELG1FQUFXQSxDQUFDO0lBQ3BDRSxTQUFTO1FBQ1JDLFNBQVM7UUFDVEMsUUFBUTtZQUNQQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsVUFBVTtZQUNWQyxRQUFRQyxrQkFBeUI7UUFDbEM7SUFDRDtJQUNBQyx5QkFBeUI7UUFDeEJDLE9BQU9GLFFBQVFHLEdBQUcsQ0FBQ0MsV0FBVztRQUM5QkMsVUFBVUwsUUFBUUcsR0FBRyxDQUFDRyxjQUFjO0lBQ3JDO0FBQ0QsR0FBRyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxsaWJcXGF1dGgwLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGxpYi9hdXRoMC5qc1xyXG5cclxuaW1wb3J0IHsgQXV0aDBDbGllbnQgfSBmcm9tIFwiQGF1dGgwL25leHRqcy1hdXRoMC9zZXJ2ZXJcIjtcclxuXHJcbi8vIEluaXRpYWxpemUgdGhlIEF1dGgwIGNsaWVudFxyXG5leHBvcnQgY29uc3QgYXV0aDAgPSBuZXcgQXV0aDBDbGllbnQoe1xyXG5cdHNlc3Npb246IHtcclxuXHRcdHJvbGxpbmc6IHRydWUsXHJcblx0XHRjb29raWU6IHtcclxuXHRcdFx0bmFtZTogXCJhcHBfc2Vzc2lvblwiLFxyXG5cdFx0XHRwYXRoOiBcIi9cIixcclxuXHRcdFx0c2FtZVNpdGU6IFwibGF4XCIsXHJcblx0XHRcdHNlY3VyZTogcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09IFwicHJvZHVjdGlvblwiLFxyXG5cdFx0fSxcclxuXHR9LFxyXG5cdGF1dGhvcml6YXRpb25QYXJhbWV0ZXJzOiB7XHJcblx0XHRzY29wZTogcHJvY2Vzcy5lbnYuQVVUSDBfU0NPUEUsXHJcblx0XHRhdWRpZW5jZTogcHJvY2Vzcy5lbnYuQVVUSDBfQVVESUVOQ0UsXHJcblx0fSxcclxufSk7XHJcbiJdLCJuYW1lcyI6WyJBdXRoMENsaWVudCIsImF1dGgwIiwic2Vzc2lvbiIsInJvbGxpbmciLCJjb29raWUiLCJuYW1lIiwicGF0aCIsInNhbWVTaXRlIiwic2VjdXJlIiwicHJvY2VzcyIsImF1dGhvcml6YXRpb25QYXJhbWV0ZXJzIiwic2NvcGUiLCJlbnYiLCJBVVRIMF9TQ09QRSIsImF1ZGllbmNlIiwiQVVUSDBfQVVESUVOQ0UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth0.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fskills%2Froute&page=%2Fapi%2Fuser%2Fskills%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fskills%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fskills%2Froute&page=%2Fapi%2Fuser%2Fskills%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fskills%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_benji_Desktop_G_PROG_Nicolas_btdash_ecosystem_btdashs_app_api_user_skills_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/user/skills/route.ts */ \"(rsc)/./app/api/user/skills/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/user/skills/route\",\n        pathname: \"/api/user/skills\",\n        filename: \"route\",\n        bundlePath: \"app/api/user/skills/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\api\\\\user\\\\skills\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_benji_Desktop_G_PROG_Nicolas_btdash_ecosystem_btdashs_app_api_user_skills_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZ1c2VyJTJGc2tpbGxzJTJGcm91dGUmcGFnZT0lMkZhcGklMkZ1c2VyJTJGc2tpbGxzJTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGdXNlciUyRnNraWxscyUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNiZW5qaSU1Q0Rlc2t0b3AlNUNHX1BST0clNUNOaWNvbGFzJTVDYnRkYXNoLWVjb3N5c3RlbSU1Q2J0ZGFzaHMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNVc2VycyU1Q2JlbmppJTVDRGVza3RvcCU1Q0dfUFJPRyU1Q05pY29sYXMlNUNidGRhc2gtZWNvc3lzdGVtJTVDYnRkYXNocyZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDcUI7QUFDdUQ7QUFDcEk7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHlHQUFtQjtBQUMzQztBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxzREFBc0Q7QUFDOUQ7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDMEY7O0FBRTFGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkM6XFxcXFVzZXJzXFxcXGJlbmppXFxcXERlc2t0b3BcXFxcR19QUk9HXFxcXE5pY29sYXNcXFxcYnRkYXNoLWVjb3N5c3RlbVxcXFxidGRhc2hzXFxcXGFwcFxcXFxhcGlcXFxcdXNlclxcXFxza2lsbHNcXFxccm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL3VzZXIvc2tpbGxzL3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvdXNlci9za2lsbHNcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL3VzZXIvc2tpbGxzL3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiQzpcXFxcVXNlcnNcXFxcYmVuamlcXFxcRGVza3RvcFxcXFxHX1BST0dcXFxcTmljb2xhc1xcXFxidGRhc2gtZWNvc3lzdGVtXFxcXGJ0ZGFzaHNcXFxcYXBwXFxcXGFwaVxcXFx1c2VyXFxcXHNraWxsc1xcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHdvcmtBc3luY1N0b3JhZ2UsXG4gICAgICAgIHdvcmtVbml0QXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fskills%2Froute&page=%2Fapi%2Fuser%2Fskills%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fskills%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:https":
/*!*****************************!*\
  !*** external "node:https" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:https");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/jose","vendor-chunks/oauth4webapi","vendor-chunks/@auth0","vendor-chunks/@edge-runtime","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fskills%2Froute&page=%2Fapi%2Fuser%2Fskills%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fskills%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();