// src/application/services/SubnetVerificationService.js

const crypto = require("crypto");
const { cryptoWaitReady, signatureVerify, decodeAddress } = require("@polkadot/util-crypto");
const { u8aToHex, hexToU8a, stringToU8a } = require("@polkadot/util");
const db = require("../../infrastructure/database/knex");
const logger = require("../../../logger");
const SecurityMonitoringService = require("./SecurityMonitoringService");

class SubnetVerificationService {
	constructor() {
		this.CHALLENGE_EXPIRY_MINUTES = 15;
		this.MAX_ATTEMPTS_PER_HOUR = 5;
		this.securityMonitoring = new SecurityMonitoringService();
		this.init();
	}

	async init() {
		try {
			await cryptoWaitReady();
			logger.info("Polkadot crypto initialized for subnet verification");
		} catch (error) {
			logger.error("Failed to initialize Polkadot crypto:", error);
		}
	}

	/**
	 * Generate a cryptographic challenge for subnet ownership verification
	 * @param {number} userId - User ID requesting verification
	 * @param {number} netuid - Subnet network UID
	 * @param {number|null} companyId - Company ID to link the subnet to (optional for user-only verification)
	 * @returns {Promise<Object>} Challenge object with message and nonce
	 */
	async generateChallenge(userId, netuid, companyId = null) {
		try {
			// Check rate limiting
			await this._checkRateLimit(userId, netuid);

			// Clean up expired challenges
			await this._cleanupExpiredChallenges();

			// Generate secure nonce
			const nonce = crypto.randomBytes(32).toString("hex");
			const timestamp = Date.now();
			const expiresAt = new Date(Date.now() + this.CHALLENGE_EXPIRY_MINUTES * 60 * 1000);

			// Create challenge message
			const challengeMessage = this._createChallengeMessage(netuid, companyId, nonce, timestamp);

			// Store challenge in database
			const [challenge] = await db("dtm_base.subnet_verification_challenges")
				.insert({
					user_id: userId,
					netuid,
					challenge_message: challengeMessage,
					nonce,
					expires_at: expiresAt,
					is_used: false,
					created_at: new Date(),
					updated_at: new Date(),
				})
				.returning("*");

			logger.info("Challenge generated for subnet verification", {
				userId,
				netuid,
				companyId,
				challengeId: challenge.id,
			});

			return {
				challengeId: challenge.id,
				message: challengeMessage,
				nonce,
				expiresAt: expiresAt.toISOString(),
				netuid,
				companyId,
			};
		} catch (error) {
			logger.error("Error generating subnet verification challenge:", {
				error: error.message,
				userId,
				netuid,
				companyId,
			});
			throw error;
		}
	}

	/**
	 * Verify subnet ownership using cryptographic signature
	 * @param {number} userId - User ID
	 * @param {number} challengeId - Challenge ID
	 * @param {string} signature - Cryptographic signature
	 * @param {string} signerAddress - Address that signed the message
	 * @param {number|null} companyId - Company ID to link the subnet to (optional for user-only verification)
	 * @returns {Promise<Object>} Verification result
	 */
	async verifyOwnership(userId, challengeId, signature, signerAddress, companyId = null) {
		const startTime = Date.now();
		let verificationResult = null;

		try {
			// Get challenge from database
			const challenge = await db("dtm_base.subnet_verification_challenges")
				.where({
					id: challengeId,
					user_id: userId,
					is_used: false,
				})
				.first();

			if (!challenge) {
				throw new Error("Invalid or expired challenge");
			}

			// Check if challenge has expired
			if (new Date() > new Date(challenge.expires_at)) {
				throw new Error("Challenge has expired");
			}

			// Get subnet information to verify ownership
			const subnet = await db("dtm_base.subnets").where({ netuid: challenge.netuid }).first();

			if (!subnet) {
				throw new Error("Subnet not found");
			}

			// Verify the signature
			const isValidSignature = await this._verifySignature(challenge.challenge_message, signature, signerAddress);

			if (!isValidSignature) {
				throw new Error("Invalid signature");
			}

			// Verify that the signer address matches the subnet owner
			const isOwner = await this._verifySubnetOwnership(signerAddress, challenge.netuid);

			if (!isOwner) {
				throw new Error("Signer is not the subnet owner");
			}

			// Mark challenge as used
			await db("dtm_base.subnet_verification_challenges").where({ id: challengeId }).update({
				is_used: true,
				updated_at: new Date(),
			});

			// Store verified ownership
			const verifiedOwnership = await this._storeVerifiedOwnership(
				userId,
				companyId,
				challenge.netuid,
				signerAddress,
				signature,
				challenge.challenge_message
			);

			// Update company netuids array
			await this._updateCompanyNetuids(companyId, challenge.netuid);

			verificationResult = {
				success: true,
				verificationId: verifiedOwnership.id,
				netuid: challenge.netuid,
				ownerAddress: signerAddress,
				verifiedAt: verifiedOwnership.verified_at,
			};

			logger.info("Subnet ownership verified successfully", {
				userId,
				netuid: challenge.netuid,
				companyId,
				verificationId: verificationResult.verificationId,
				duration: Date.now() - startTime,
			});

			return verificationResult;
		} catch (error) {
			verificationResult = {
				success: false,
				error: error.message,
			};

			logger.warn("Subnet ownership verification failed", {
				userId,
				challengeId,
				companyId,
				error: error.message,
				duration: Date.now() - startTime,
			});

			throw error;
		} finally {
			// Log verification attempt
			await this._logVerificationAttempt(userId, challengeId, verificationResult, signerAddress);
		}
	}

	/**
	 * Get verified subnets for a company
	 * @param {number} companyId - Company ID
	 * @returns {Promise<Array>} Array of verified subnets
	 */
	async getVerifiedSubnets(companyId) {
		try {
			const verifiedSubnets = await db("dtm_base.verified_subnet_ownership as vso")
				.join("dtm_base.subnets as s", "vso.netuid", "s.netuid")
				.where({
					"vso.company_id": companyId,
					"vso.is_active": true,
				})
				.select(
					"vso.id as verification_id",
					"vso.netuid",
					"vso.owner_address",
					"vso.verified_at",
					"s.name as subnet_name",
					"s.description",
					"s.image_url"
				)
				.orderBy("vso.verified_at", "desc");

			return verifiedSubnets;
		} catch (error) {
			logger.error("Error getting verified subnets:", {
				error: error.message,
				companyId,
			});
			throw error;
		}
	}

	/**
	 * Get verified subnets for a user (for profile management)
	 * @param {number} userId - User ID
	 * @returns {Promise<Array>} Array of verified subnets
	 */
	async getVerifiedSubnetsForUser(userId) {
		try {
			const verifiedSubnets = await db("dtm_base.verified_subnet_ownership as vso")
				.join("dtm_base.subnets as s", "vso.netuid", "s.netuid")
				.where({
					"vso.user_id": userId,
					"vso.is_active": true,
				})
				.select(
					"vso.id as verification_id",
					"vso.netuid",
					"vso.owner_address",
					"vso.verified_at",
					"vso.company_id",
					"s.name as subnet_name",
					"s.description",
					"s.description_short",
					"s.image_url",
					"s.owner_managed_fields",
					"s.owner_managed_at",
					"s.owner_managed_by"
				)
				.orderBy("vso.verified_at", "desc");

			return verifiedSubnets;
		} catch (error) {
			logger.error("Error getting verified subnets for user:", {
				error: error.message,
				userId,
			});
			throw error;
		}
	}

	/**
	 * Get verified subnets for a user (both company-based and user-only)
	 * @param {number} userId - User ID
	 * @returns {Promise<Array>} Array of verified subnet ownership records
	 */
	async getUserVerifiedSubnets(userId) {
		try {
			const verifiedSubnets = await db("dtm_base.verified_subnet_ownership as vso")
				.leftJoin("dtm_base.subnets as s", "vso.netuid", "s.netuid")
				.leftJoin("dtm_base.companies as c", "vso.company_id", "c.id")
				.where("vso.user_id", userId)
				.where("vso.is_active", true)
				.select(
					"vso.*",
					"s.name as subnet_name",
					"s.description as subnet_description",
					"c.name as company_name"
				)
				.orderBy("vso.verified_at", "desc");

			logger.info("Retrieved verified subnets for user", { userId, count: verifiedSubnets.length });
			return verifiedSubnets;
		} catch (error) {
			logger.error("Error getting user verified subnets:", { error, userId });
			throw error;
		}
	}

	/**
	 * Remove subnet verification (for company owners or user-only verification)
	 * @param {number} userId - User ID
	 * @param {number|null} companyId - Company ID (null for user-only verification)
	 * @param {number} netuid - Subnet network UID
	 * @returns {Promise<boolean>} Success status
	 */
	async removeVerification(userId, companyId, netuid) {
		try {
			if (companyId) {
				// Company-based verification: check permissions
				const userCompany = await db("dtm_base.user_company")
					.where({
						user_id: userId,
						company_id: companyId,
					})
					.first();

				if (!userCompany || !["owner", "admin"].includes(userCompany.role)) {
					throw new Error("Insufficient permissions");
				}

				// Delete company-based verification
				const deleted = await db("dtm_base.verified_subnet_ownership")
					.where({
						company_id: companyId,
						netuid: netuid,
					})
					.del();

				if (deleted === 0) {
					throw new Error("Verification not found");
				}

				// Remove netuid from company netuids array
				await this._removeFromCompanyNetuids(companyId, netuid);
			} else {
				// User-only verification: user can only remove their own
				const deleted = await db("dtm_base.verified_subnet_ownership")
					.where({
						user_id: userId,
						company_id: null,
						netuid: netuid,
					})
					.del();

				if (deleted === 0) {
					throw new Error("Verification not found");
				}
			}

			// Check if there are any other active verifications for this subnet
			const remainingVerifications = await db("dtm_base.verified_subnet_ownership")
				.where({
					netuid: netuid,
					is_active: true,
				})
				.count("* as count")
				.first();

			const hasRemainingVerifications = parseInt(remainingVerifications.count) > 0;

			// Update subnet claimed status only if no other verifications exist
			if (!hasRemainingVerifications) {
				try {
					await db("dtm_base.subnets").where({ netuid }).update({
						claimed: false,
						claimed_date: null,
						updated_at: new Date(),
					});

					logger.info("Subnet marked as unclaimed", {
						netuid,
						userId,
						companyId,
					});
				} catch (error) {
					logger.error("Failed to update subnet unclaimed status", {
						netuid,
						userId,
						companyId,
						error: error.message,
					});
					// Don't throw here as the verification removal was successful
					// The claimed status update is supplementary
				}
			} else {
				logger.info("Subnet remains claimed due to other active verifications", {
					netuid,
					remainingCount: remainingVerifications.count,
				});
			}

			logger.info("Subnet verification removed", {
				userId,
				companyId,
				netuid,
				subnetUnclaimed: !hasRemainingVerifications,
			});

			return true;
		} catch (error) {
			logger.error("Error removing subnet verification:", {
				error: error.message,
				userId,
				companyId,
				netuid,
			});
			throw error;
		}
	}

	/**
	 * Create challenge message for signing
	 * @private
	 */
	_createChallengeMessage(netuid, companyId, nonce, timestamp) {
		const companyLine = companyId ? `Company: ${companyId}` : "Verification Type: User-only";
		return `BTDash Subnet Verification
Subnet: ${netuid}
${companyLine}
Nonce: ${nonce}
Timestamp: ${timestamp}
Please sign this message to verify ownership of subnet ${netuid}`;
	}

	/**
	 * Verify cryptographic signature
	 * @private
	 */
	async _verifySignature(message, signature, signerAddress) {
		try {
			const messageU8a = stringToU8a(message);
			const signatureU8a = hexToU8a(signature);
			const addressU8a = decodeAddress(signerAddress);

			const result = signatureVerify(messageU8a, signatureU8a, addressU8a);
			return result.isValid;
		} catch (error) {
			logger.error("Signature verification error:", error);
			return false;
		}
	}

	/**
	 * Verify subnet ownership on-chain
	 * @private
	 */
	async _verifySubnetOwnership(signerAddress, netuid) {
		try {
			const subnet = await db("dtm_base.subnets").where({ netuid }).first();

			if (!subnet) {
				return false;
			}

			// Compare addresses (both hex and SS58 formats)
			return subnet.owner_address === signerAddress || subnet.sub_address_pkey === signerAddress;
		} catch (error) {
			logger.error("Error verifying subnet ownership:", error);
			return false;
		}
	}

	/**
	 * Store verified ownership record
	 * @private
	 */
	async _storeVerifiedOwnership(userId, companyId, netuid, ownerAddress, signature, message) {
		const currentDate = new Date();

		// Check if subnet is already verified by someone else
		const existingVerification = await db("dtm_base.verified_subnet_ownership")
			.where({ netuid, is_active: true })
			.first();

		if (existingVerification) {
			// If it's the same user, update the record
			if (existingVerification.user_id === userId) {
				const [verifiedOwnership] = await db("dtm_base.verified_subnet_ownership")
					.where({ netuid, user_id: userId })
					.update({
						company_id: companyId,
						owner_address: ownerAddress,
						verification_signature: signature,
						verification_message: message,
						verified_at: currentDate,
						is_active: true,
						updated_at: currentDate,
					})
					.returning("*");
				return verifiedOwnership;
			} else {
				// Different user trying to verify same subnet
				throw new Error(`Subnet ${netuid} is already verified by another user`);
			}
		}

		// No existing verification, create new record
		const [verifiedOwnership] = await db("dtm_base.verified_subnet_ownership")
			.insert({
				user_id: userId,
				company_id: companyId,
				netuid,
				owner_address: ownerAddress,
				verification_signature: signature,
				verification_message: message,
				verified_at: currentDate,
				is_active: true,
				created_at: currentDate,
				updated_at: currentDate,
			})
			.returning("*");

		// Update the subnet record to mark it as claimed
		try {
			const updateResult = await db("dtm_base.subnets").where({ netuid }).update({
				claimed: true,
				claimed_date: currentDate,
				updated_at: currentDate,
			});

			if (updateResult === 0) {
				logger.warn("No subnet found to update claimed status", {
					netuid,
					userId,
					companyId,
				});
			} else {
				logger.info("Subnet marked as claimed", {
					netuid,
					userId,
					companyId,
					claimedDate: currentDate,
				});
			}
		} catch (error) {
			logger.error("Failed to update subnet claimed status", {
				netuid,
				userId,
				companyId,
				error: error.message,
			});
			// Don't throw here as the verification was successful
			// The claimed status update is supplementary
			// We could schedule a retry or manual fix later
		}

		return verifiedOwnership;
	}

	/**
	 * Update company netuids array
	 * @private
	 */
	async _updateCompanyNetuids(companyId, netuid) {
		const company = await db("dtm_base.companies").where({ id: companyId }).first();

		if (company) {
			const netuids = Array.isArray(company.netuids) ? company.netuids : [];
			if (!netuids.includes(netuid)) {
				netuids.push(netuid);
				await db("dtm_base.companies").where({ id: companyId }).update({
					netuids,
					updated_at: new Date(),
				});
			}
		}
	}

	/**
	 * Remove netuid from company netuids array
	 * @private
	 */
	async _removeFromCompanyNetuids(companyId, netuid) {
		const company = await db("dtm_base.companies").where({ id: companyId }).first();

		if (company && Array.isArray(company.netuids)) {
			const netuids = company.netuids.filter((id) => id !== netuid);
			await db("dtm_base.companies").where({ id: companyId }).update({
				netuids,
				updated_at: new Date(),
			});
		}
	}

	/**
	 * Check rate limiting for verification attempts
	 * @private
	 */
	async _checkRateLimit(userId, netuid) {
		const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

		const attemptCount = await db("dtm_base.subnet_verification_attempts")
			.where({
				user_id: userId,
				netuid: netuid,
			})
			.where("created_at", ">", oneHourAgo)
			.count("id as count")
			.first();

		if (attemptCount.count >= this.MAX_ATTEMPTS_PER_HOUR) {
			throw new Error("Rate limit exceeded. Please try again later.");
		}
	}

	/**
	 * Clean up expired challenges
	 * @private
	 */
	async _cleanupExpiredChallenges() {
		const deleted = await db("dtm_base.subnet_verification_challenges").where("expires_at", "<", new Date()).del();

		if (deleted > 0) {
			logger.info(`Cleaned up ${deleted} expired challenges`);
		}
	}

	/**
	 * Log verification attempt for security monitoring
	 * @private
	 */
	async _logVerificationAttempt(userId, challengeId, result, attemptedAddress) {
		try {
			const challenge = await db("dtm_base.subnet_verification_challenges").where({ id: challengeId }).first();

			await db("dtm_base.subnet_verification_attempts").insert({
				user_id: userId,
				netuid: challenge?.netuid,
				attempted_address: attemptedAddress,
				challenge_id: challengeId,
				success: result?.success || false,
				failure_reason: result?.error || null,
				created_at: new Date(),
			});
		} catch (error) {
			logger.error("Error logging verification attempt:", error);
		}
	}

	/**
	 * Verify and fix subnet claimed status consistency
	 * This method checks if a subnet's claimed status matches its verification records
	 * @param {number} netuid - Subnet network UID
	 * @returns {Promise<boolean>} True if status was corrected, false if already consistent
	 */
	async verifySubnetClaimedStatus(netuid) {
		try {
			// Get current subnet record
			const subnet = await db("dtm_base.subnets").where({ netuid }).first();
			if (!subnet) {
				logger.warn("Subnet not found for claimed status verification", { netuid });
				return false;
			}

			// Check for active verifications
			const activeVerifications = await db("dtm_base.verified_subnet_ownership")
				.where({
					netuid: netuid,
					is_active: true,
				})
				.count("* as count")
				.first();

			const hasActiveVerifications = parseInt(activeVerifications.count) > 0;
			const shouldBeClaimed = hasActiveVerifications;
			const currentlyClaimed = subnet.claimed;

			// Fix inconsistency if found
			if (shouldBeClaimed !== currentlyClaimed) {
				await db("dtm_base.subnets")
					.where({ netuid })
					.update({
						claimed: shouldBeClaimed,
						claimed_date: shouldBeClaimed ? new Date() : null,
						updated_at: new Date(),
					});

				logger.info("Fixed subnet claimed status inconsistency", {
					netuid,
					previousStatus: currentlyClaimed,
					newStatus: shouldBeClaimed,
					activeVerifications: activeVerifications.count,
				});

				return true;
			}

			return false;
		} catch (error) {
			logger.error("Error verifying subnet claimed status", {
				netuid,
				error: error.message,
			});
			throw error;
		}
	}
}

module.exports = SubnetVerificationService;
