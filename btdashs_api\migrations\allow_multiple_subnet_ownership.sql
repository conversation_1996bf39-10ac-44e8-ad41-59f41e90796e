-- Migration: Allow multiple users to verify ownership of the same subnet
-- This removes the unique constraint on netuid to allow multiple users to prove ownership

BEGIN;

-- Drop the unique constraint on netuid to allow multiple users to verify the same subnet
ALTER TABLE dtm_base.verified_subnet_ownership 
DROP CONSTRAINT IF EXISTS verified_subnet_ownership_netuid_unique;

-- Drop any other potential unique constraints that might prevent multiple ownership
ALTER TABLE dtm_base.verified_subnet_ownership 
DROP CONSTRAINT IF EXISTS verified_subnet_ownership_netuid_company_id_key;

-- Create an index for better query performance on active verifications
CREATE INDEX IF NOT EXISTS idx_verified_subnet_ownership_active 
ON dtm_base.verified_subnet_ownership (netuid, is_active) 
WHERE is_active = true;

-- Create an index for better query performance on user-subnet combinations
CREATE INDEX IF NOT EXISTS idx_verified_subnet_ownership_user_netuid 
ON dtm_base.verified_subnet_ownership (user_id, netuid, is_active);

-- Create an index for better query performance on company-subnet combinations
CREATE INDEX IF NOT EXISTS idx_verified_subnet_ownership_company_netuid 
ON dtm_base.verified_subnet_ownership (company_id, netuid, is_active);

COMMIT;
